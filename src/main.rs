use anyhow::{anyhow, Result};
use clap::{value_parser, Arg, Command};
use std::thread;
use std::time::{Duration, Instant};
use std::{os::unix::fs::PermissionsExt, path::PathBuf, process::Stdio};

const FILE_PATH: &str = "/usr/lib/app-init.d";
const LINK_PATH: &str = "/etc/app-init.d";

const VERSION: &str = "v1.0.0";

fn main() -> Result<()> {
    let m = Command::new("app")
        .version(VERSION)
        .subcommand(Command::new("list").about("list all init scripts"))
        .subcommand(
            Command::new("enable").about("enable init script").arg(
                Arg::new("name")
                    .help("name of the init script, 'all' for all scripts")
                    .required(true)
                    .index(1)
                    .value_parser(value_parser!(String)),
            ),
        )
        .subcommand(
            Command::new("disable").about("disable init script").arg(
                Arg::new("name")
                    .help("name of the init script, 'all' for all scripts")
                    .required(true)
                    .index(1)
                    .value_parser(value_parser!(String)),
            ),
        )
        .subcommand(
            Command::new("start").about("start init script").arg(
                Arg::new("name")
                    .help("name of the init script, 'all' for all scripts")
                    .required(true)
                    .index(1)
                    .value_parser(value_parser!(String)),
            ),
        )
        .subcommand(
            Command::new("stop").about("stop init script").arg(
                Arg::new("name")
                    .help("name of the init script, 'all' for all scripts")
                    .required(true)
                    .index(1)
                    .value_parser(value_parser!(String)),
            ),
        )
        .subcommand(
            Command::new("ver").about("get version of app").arg(
                Arg::new("name")
                    .help("name of the app")
                    .required(true)
                    .index(1)
                    .value_parser(value_parser!(String)),
            ),
        )
        // .subcommand(Command::new("status").about("apps' running status"))
        .arg_required_else_help(true)
        .get_matches();

    match m.subcommand() {
        Some(("list", _)) => {
            println!(
                "{:<20}{:<10}{:<10}{}",
                "Appname", "Stage", "Status", "Running"
            );
            let re = regex::Regex::new(r"^(S\d+)(.*)").unwrap();
            // let daemon_re = regex::Regex::new(r"DAEMON=(\S+)")?;
            let entries = entries_in_order(FILE_PATH)?;
            for entry in entries {
                let name = entry.file_name().unwrap().to_string_lossy();
                let status = if std::path::Path::new(&format!("{}/{}", LINK_PATH, name)).exists() {
                    "enabled"
                } else {
                    "disabled"
                };

                if let Some(cap) = re.captures(&name) {
                    /* let content = std::fs::read_to_string(entry.as_path())?;
                    let name = if let Some(daemon_cap) = daemon_re.captures(&content) {
                        daemon_cap[1].to_string()
                    } else {
                       cap[2].to_string()
                    };
                    let rflag: &str = if std::process::Command::new("pgrep").args(["-x", &name]).output()?.status.success() {
                       "True"
                    } else {
                        "False"
                    }; */
                    let rflag = if check_status(&entry).is_ok() {
                        "True"
                    } else {
                        "False"
                    };
                    println!("{:<20}{:<10}{:<10}{}", &cap[2], &cap[1], status, rflag);
                }
            }
        }
        Some(("enable", args)) => {
            let name: &String = args.get_one("name").unwrap();
            add_permission(name)?;
            if name == "all" {
                for entry in std::fs::read_dir(FILE_PATH)? {
                    let entry = entry.unwrap();
                    let name = entry.file_name().into_string().unwrap();
                    let _ = std::os::unix::fs::symlink(
                        &entry.path(),
                        format!("{}/{}", LINK_PATH, name),
                    );
                }
            } else {
                let file = find_file(name, FILE_PATH)?;
                let filename = file.file_name().unwrap().to_string_lossy();
                let link_path = format!("{}/{}", LINK_PATH, filename);
                if !std::path::Path::new(&link_path).exists() {
                    std::os::unix::fs::symlink(&file, format!("{}/{}", LINK_PATH, filename))?;
                }
            }
        }
        Some(("disable", args)) => {
            let name: &String = args.get_one("name").unwrap();
            if name == "all" {
                for entry in std::fs::read_dir(LINK_PATH)? {
                    let entry = entry.unwrap();
                    std::fs::remove_file(entry.path())?;
                }
            } else {
                if let Ok(file) = find_file(name, LINK_PATH) {
                    std::fs::remove_file(&file)?;
                }
            }
        }
        Some(("start", args)) => {
            let name: &String = args.get_one("name").unwrap();
            add_permission(name)?;
            if name == "all" {
                let entries = entries_in_order(LINK_PATH)?;
                for entry in entries {
                    // let entry = entry.unwrap();
                    std::process::Command::new(&entry)
                        .arg("start")
                        .stdout(Stdio::null())
                        .status()?;
                }
            } else {
                let file = find_file(name, FILE_PATH)?;
                let stat = std::process::Command::new(&file)
                    .arg("start")
                    .stdout(Stdio::null())
                    .status()?;
                if stat.success() {
                    check_status_timeout(&file)?;
                } else {
                    return Err(anyhow!("start fail"));
                }
            }
        }
        Some(("stop", args)) => {
            let name: &String = args.get_one("name").unwrap();
            add_permission(name)?;
            if name == "all" {
                let mut entries = entries_in_order(LINK_PATH)?;
                entries.reverse();
                for entry in entries {
                    // let entry = entry.unwrap();
                    std::process::Command::new(&entry)
                        .arg("stop")
                        .stdout(Stdio::null())
                        .status()?;
                }
            } else {
                let file = find_file(name, FILE_PATH)?;
                let stat = std::process::Command::new(&file)
                    .arg("stop")
                    .stdout(Stdio::null())
                    .status()?;
                if !stat.success() {
                    return Err(anyhow!("stop fail"));
                }
            }
        }
        Some(("ver", args)) => {
            let name: &String = args.get_one("name").unwrap();
            add_permission(name)?;
            let file = find_file(name, FILE_PATH)?;
            let output = std::process::Command::new(&file).arg("version").output();
            if output.is_ok() && output.as_ref().unwrap().status.success() {
                print!("{}", String::from_utf8(output.unwrap().stdout)?);
            } else {
                return Err(anyhow!("unkown version"));
            }
        }
        _ => {
            return Err(anyhow!("No subcommand was used"));
        }
    }
    Ok(())
}

fn find_file(name: &str, path: &str) -> Result<PathBuf> {
    let mut files: Vec<PathBuf> = vec![];
    let re = regex::Regex::new(r"(S\d+)(.*)").unwrap();
    for entry in std::fs::read_dir(path).unwrap() {
        let entry = entry.unwrap();
        let filename = entry.file_name().to_string_lossy().to_string();

        if filename.eq(name) {
            files.push(entry.path());
            continue;
        }
        if let Some(cap) = re.captures(&filename) {
            if cap
                .get(2)
                .expect(&format!("invalid filename:{}", &cap[0]))
                .as_str()
                .eq(name)
            {
                files.push(entry.path());
            }
        }
    }
    match files.len() {
        0 => Err(anyhow!("no such file")),
        1 => Ok(files[0].clone()),
        _ => Err(anyhow!("multiple files found: {:?}", files)),
    }
}

fn entries_in_order<P>(path: P) -> Result<Vec<PathBuf>>
where
    P: AsRef<std::path::Path>,
{
    let mut entries = std::fs::read_dir(path)?
        .map(|res| res.map(|e| e.path()))
        .collect::<Result<Vec<_>, std::io::Error>>()?;
    entries.sort();
    Ok(entries)
}

fn add_permission(name: &str) -> Result<()> {
    if name == "all" {
        let out = std::process::Command::new("chmod")
            .args(["+x", "-R", FILE_PATH])
            .output()?;
        if !out.status.success() {
            return Err(anyhow!("添加执行权限失败"));
        }
    } else {
        let file = find_file(name, FILE_PATH)?;
        let permissions = std::fs::metadata(file.as_path())?.permissions();
        let mode = permissions.mode();
        if mode & 0o111 != 0o111 {
            let new_mode = mode | 0o111;
            let mut new_permissions = permissions;
            new_permissions.set_mode(new_mode);
            std::fs::set_permissions(file.as_path(), new_permissions)?;
        }
    }
    Ok(())
}
fn check_status(file: &PathBuf) -> Result<()> {
    let name = file.file_name().unwrap().to_string_lossy();
    let re = regex::Regex::new(r"^(S\d+)(.*)").unwrap();
    let daemon_re = regex::Regex::new(r"DAEMON=(\S+)")?;
    if let Some(cap) = re.captures(&name) {
        let content = std::fs::read_to_string(file.as_path())?;
        let name = if let Some(daemon_cap) = daemon_re.captures(&content) {
            daemon_cap[1].to_string()
        } else {
            cap[2].to_string()
        };
        let status = std::process::Command::new("pidof")
            .args(["-x", &name])
            .output()?
            .status;
        if status.success() {
            return Ok(());
        } else {
            return Err(anyhow!(
                "execute \"pidof -x {}\" exit with {}",
                name,
                status.code().unwrap()
            ));
        }
    }
    Err(anyhow!("invalid script name {name}"))
}

fn check_status_timeout(file: &PathBuf) -> Result<()> {
    let timeout_duration = Duration::from_secs(5);
    let interval_duration = Duration::from_millis(100);
    let start_time = Instant::now();
    let mut err: Option<anyhow::Error> = None;
    loop {
        if start_time.elapsed() >= timeout_duration {
            return Err(anyhow!("timeout, err: {}", err.unwrap()));
        }

        thread::sleep(interval_duration);

        match check_status(file) {
            Ok(_) => {
                return Ok(());
            }
            Err(e) => {
                err = Some(e);
                continue;
            }
        }
    }
}
