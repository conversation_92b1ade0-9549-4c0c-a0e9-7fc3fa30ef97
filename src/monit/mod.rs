use serde::{Deserialize, Serialize};
use zbus::zvariant::Type;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Type, PartialEq)]
pub struct LoadAve {
    pub load_1: f32,
    pub load_5: f32,
    pub load_15: f32,
}

impl LoadAve {
    pub fn new(load_1: f32, load_5: f32, load_15: f32) -> Self {
        Self {
            load_1,
            load_5,
            load_15,
        }
    }
}

#[cfg(test)]
mod tests {
    use zvariant::Signature;

    use super::*;

    #[test]
    fn test_load_ave_signature() {
        assert_eq!(
            LoadAve::SIGNATURE,
            &Signature::Structure(zvariant::signature::Fields::Static {
                fields: &[&Signature::F64, &Signature::F64, &Signature::F64]
            })
        );
    }
}
