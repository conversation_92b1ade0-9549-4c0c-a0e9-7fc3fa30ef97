use super::task::{<PERSON><PERSON>onfig, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ResourceLimit, ResourceLimitAct};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::time::Duration;

/// Configuration for the daemon manager
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ManagerConfig {
    /// Global monitoring interval in seconds
    pub global_interval: u64,
    /// Maximum number of concurrent tasks
    pub max_tasks: usize,
    /// Path to store daemon state and logs
    pub state_dir: PathBuf,
    /// Default configuration for new tasks
    pub default_task_config: DaemonConfig,
    /// Whether to enable detailed logging
    pub verbose_logging: bool,
    /// Auto-restart failed tasks
    pub auto_restart: bool,
    /// Maximum restart attempts per task
    pub max_restart_attempts: u32,
    /// channel size
    pub channel_size: usize,
}

impl Default for ManagerConfig {
    fn default() -> Self {
        Self {
            global_interval: 10,
            max_tasks: 100,
            state_dir: PathBuf::from("/var/lib/daemon-manager"),
            default_task_config: DaemonConfig::default(),
            verbose_logging: false,
            auto_restart: true,
            max_restart_attempts: 3,
            channel_size: 30,
        }
    }
}

impl ManagerConfig {
    /// Create a new manager configuration with default values
    pub fn new() -> Self {
        Self::default()
    }

    /// Create a configuration with custom global interval
    pub fn with_interval(mut self, interval_secs: u64) -> Self {
        self.global_interval = interval_secs;
        self
    }

    /// Set maximum number of concurrent tasks
    pub fn with_max_tasks(mut self, max_tasks: usize) -> Self {
        self.max_tasks = max_tasks;
        self
    }

    /// Set state directory path
    pub fn with_state_dir<P: Into<PathBuf>>(mut self, path: P) -> Self {
        self.state_dir = path.into();
        self
    }

    /// Enable or disable verbose logging
    pub fn with_verbose_logging(mut self, enabled: bool) -> Self {
        self.verbose_logging = enabled;
        self
    }

    /// Configure auto-restart behavior
    pub fn with_auto_restart(mut self, enabled: bool, max_attempts: u32) -> Self {
        self.auto_restart = enabled;
        self.max_restart_attempts = max_attempts;
        self
    }

    /// Set default task configuration
    pub fn with_default_task_config(mut self, config: DaemonConfig) -> Self {
        self.default_task_config = config;
        self
    }

    /// Get the global interval as Duration
    pub fn global_interval_duration(&self) -> Duration {
        Duration::from_secs(self.global_interval)
    }

    /// Validate the configuration
    pub fn validate(&self) -> Result<(), String> {
        if self.global_interval == 0 {
            return Err("Global interval cannot be zero".to_string());
        }
        if self.max_tasks == 0 {
            return Err("Max tasks cannot be zero".to_string());
        }
        if self.default_task_config.interval == 0 {
            return Err("Default task interval cannot be zero".to_string());
        }
        Ok(())
    }
}

/// Helper functions to create common resource limits and configurations
pub mod presets {
    use super::*;

    /// Create a CPU usage limit with specified threshold and action
    pub fn cpu_limit(threshold_percent: f32, action: ResourceLimitAct) -> ResourceLimit {
        ResourceLimit {
            threshold: ProcThreshold::Cpu(threshold_percent),
            action,
        }
    }

    /// Create a memory usage limit with specified threshold and action
    pub fn memory_limit(threshold_bytes: u64, action: ResourceLimitAct) -> ResourceLimit {
        ResourceLimit {
            threshold: ProcThreshold::Memory(threshold_bytes),
            action,
        }
    }

    /// Create a standard set of resource limits for server processes
    pub fn server_limits() -> Vec<ResourceLimit> {
        vec![
            cpu_limit(90.0, ResourceLimitAct::Warn),
            cpu_limit(95.0, ResourceLimitAct::Kill),
            memory_limit(1024 * 1024 * 1024, ResourceLimitAct::Warn), // 1GB warning
            memory_limit(2 * 1024 * 1024 * 1024, ResourceLimitAct::Kill), // 2GB kill
        ]
    }

    /// Create a standard set of resource limits for background processes
    pub fn background_limits() -> Vec<ResourceLimit> {
        vec![
            cpu_limit(50.0, ResourceLimitAct::Warn),
            cpu_limit(70.0, ResourceLimitAct::Kill),
            memory_limit(512 * 1024 * 1024, ResourceLimitAct::Warn), // 512MB warning
            memory_limit(1024 * 1024 * 1024, ResourceLimitAct::Kill), // 1GB kill
        ]
    }

    /// Create a standard set of resource limits for critical processes
    pub fn critical_limits() -> Vec<ResourceLimit> {
        vec![
            cpu_limit(95.0, ResourceLimitAct::Warn),
            memory_limit(2 * 1024 * 1024 * 1024, ResourceLimitAct::Warn), // 2GB warning
        ]
    }

    /// Create a task configuration for server processes
    pub fn server_config() -> DaemonConfig {
        DaemonConfig {
            limits: server_limits(),
            interval: 30,
        }
    }

    /// Create a task configuration for background processes
    pub fn background_config() -> DaemonConfig {
        DaemonConfig {
            limits: background_limits(),
            interval: 60,
        }
    }

    /// Create a task configuration for critical processes
    pub fn critical_config() -> DaemonConfig {
        DaemonConfig {
            limits: critical_limits(),
            interval: 15,
        }
    }
}
