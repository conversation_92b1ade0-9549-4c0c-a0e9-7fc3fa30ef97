use thiserror::Error;

#[derive(Error, Debug)]
pub enum DaemonError {
    #[error("Failed to create daemon: {0}")]
    CreateError(String),
    #[error("Failed to update daemon: {0}")]
    UpdateError(String),
    #[error("Failed to delete daemon: {0}")]
    DeleteError(String),
    #[error("Failed to list daemons: {0}")]
    ListError(String),
    #[error("Failed to get daemon: {0}")]
    GetError(String),
    #[error("Failed to set daemon: {0}")]
    SetError(String),
    #[error("Failed to start daemon: {0}")]
    StartError(String),
    #[error("Failed to stop daemon: {0}")]
    StopError(String),
    #[error("Failed to restart daemon: {0}")]
    RestartError(String),
    #[error("Failed to check daemon: {0}")]
    CheckError(String),
    #[error("Failed to configure daemon: {0}")]
    ConfigureError(String),
    #[error("Failed to upgrade daemon: {0}")]
    UpgradeError(String),
    #[error("Failed to clean daemon: {0}")]
    CleanError(String),
    #[error("Failed to get config: {0}")]
    GetConfigError(String),
    #[error("Failed to set config: {0}")]
    SetConfigError(String),
    #[error("Failed to get process: {0}")]
    ProcessError(String),
}

pub type Result<T> = std::result::Result<T, DaemonError>;
