# Daemon Process Monitoring Module

A sophisticated, production-ready process monitoring and resource management system built with Rust and Tokio. This module provides comprehensive process monitoring capabilities with configurable resource limits, automatic restart functionality, and flexible management options.

## Features

### 🔍 **Process Monitoring**
- Real-time CPU and memory usage tracking
- Configurable monitoring intervals
- Process lifecycle management
- Automatic process discovery and monitoring

### ⚡ **Resource Management**
- CPU usage limits with configurable thresholds
- Memory usage limits with byte-level precision
- Extensible framework for disk and network I/O monitoring
- Multiple action types: Warn, Kill, Limit

### 🔄 **Automatic Recovery**
- Configurable auto-restart functionality
- Maximum restart attempt limits
- Intelligent failure detection
- Graceful task lifecycle management

### 🏗️ **Flexible Architecture**
- Builder pattern for easy configuration
- Preset configurations for common use cases
- Modular design with clean separation of concerns
- Async/await support throughout

## Quick Start

### Basic Usage

```rust
use app::daemon::{utils, DaemonManager, presets};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Create a daemon manager with default settings
    let mut manager = utils::create_default_manager()?;
    
    // Add a task to monitor the current process
    let current_pid = std::process::id();
    manager.add_task(
        "my_process".to_string(),
        current_pid,
        Some(presets::server_config()),
    )?;
    
    // Start monitoring
    manager.start_task("my_process")?;
    
    println!("Monitoring {} tasks", manager.task_count());
    Ok(())
}
```

### Advanced Configuration

```rust
use app::daemon::{builder::DaemonManagerBuilder, presets, ManagerConfig};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Create a custom configuration
    let config = ManagerConfig::default()
        .with_interval(15)  // Check every 15 seconds
        .with_max_tasks(50)
        .with_verbose_logging(true)
        .with_auto_restart(true, 3);  // Max 3 restart attempts
    
    // Build the manager
    let mut manager = DaemonManagerBuilder::new()
        .with_config(config)
        .build()?;
    
    // Add multiple tasks with different configurations
    manager.add_task("critical_service".to_string(), 1234, Some(presets::critical_config()))?;
    manager.add_task("background_job".to_string(), 5678, Some(presets::background_config()))?;
    
    // Start all tasks
    manager.start_all_tasks()?;
    
    Ok(())
}
```

## Configuration Options

### Manager Configuration

```rust
use app::daemon::ManagerConfig;

let config = ManagerConfig::default()
    .with_interval(30)              // Global monitoring interval (seconds)
    .with_max_tasks(100)            // Maximum concurrent tasks
    .with_verbose_logging(true)     // Enable detailed logging
    .with_auto_restart(true, 5)     // Auto-restart with max 5 attempts
    .with_state_dir("/var/lib/daemon"); // State directory path
```

### Resource Limits

```rust
use app::daemon::{DaemonConfig, ResourceLimit, ProcThreshold, ResourceLimitAct};

let task_config = DaemonConfig {
    limits: vec![
        ResourceLimit {
            threshold: ProcThreshold::Cpu(90.0),        // 90% CPU
            action: ResourceLimitAct::Warn,
        },
        ResourceLimit {
            threshold: ProcThreshold::Memory(1024 * 1024 * 1024), // 1GB RAM
            action: ResourceLimitAct::Kill,
        },
    ],
    interval: 10, // Check every 10 seconds
};
```

## Preset Configurations

The module includes several preset configurations for common use cases:

### Server Processes
```rust
let config = presets::server_config();
// - CPU: Warn at 90%, Kill at 95%
// - Memory: Warn at 1GB, Kill at 2GB
// - Interval: 30 seconds
```

### Background Processes
```rust
let config = presets::background_config();
// - CPU: Warn at 50%, Kill at 70%
// - Memory: Warn at 512MB, Kill at 1GB
// - Interval: 60 seconds
```

### Critical Processes
```rust
let config = presets::critical_config();
// - CPU: Warn at 95%
// - Memory: Warn at 2GB
// - Interval: 15 seconds
// - No automatic killing
```

## API Reference

### DaemonManager

Main interface for managing process monitoring tasks.

#### Key Methods
- `add_task(name, pid, config)` - Add a new monitoring task
- `start_task(name)` - Start monitoring a task
- `stop_task(name)` - Stop monitoring a task
- `remove_task(name)` - Remove a task completely
- `restart_task(name)` - Restart a task
- `list_tasks()` - Get status of all tasks
- `start_all_tasks()` - Start all ready tasks
- `stop_all_tasks()` - Stop all running tasks

### TaskProcess

Represents an individual monitoring task.

#### Key Properties
- `pid` - Process ID being monitored
- `name` - Task identifier
- `state` - Current task state (Ready, Running, Stopped)
- `config` - Task-specific configuration
- `restart_attempts` - Number of restart attempts

### Builder Pattern

Use `DaemonManagerBuilder` for flexible configuration:

```rust
let manager = DaemonManagerBuilder::new()
    .with_interval(20)
    .with_max_tasks(30)
    .with_verbose_logging()
    .with_auto_restart(5)
    .build()?;
```

## Error Handling

The module uses a comprehensive error system with specific error types:

```rust
use app::daemon::{DaemonError, Result};

match manager.add_task("test".to_string(), 1234, None) {
    Ok(_) => println!("Task added successfully"),
    Err(DaemonError::CreateError(msg)) => eprintln!("Failed to create task: {}", msg),
    Err(DaemonError::GetError(msg)) => eprintln!("Task not found: {}", msg),
    Err(e) => eprintln!("Other error: {}", e),
}
```

## Logging

The module provides comprehensive logging at different levels:

- **INFO**: Task lifecycle events, configuration changes
- **WARN**: Resource limit violations, restart attempts
- **ERROR**: Critical failures, task crashes
- **DEBUG**: Detailed monitoring information

Enable verbose logging in the manager configuration for detailed output.

## Testing

Run the test suite:

```bash
cargo test daemon --lib
```

The module includes comprehensive tests covering:
- Configuration validation
- Task lifecycle management
- Resource limit enforcement
- Builder pattern functionality
- Error handling scenarios

## Examples

See `src/daemon/examples.rs` for complete working examples including:
- Basic daemon manager setup
- Custom configuration examples
- Multi-process monitoring
- Preset configuration usage
- Error handling patterns

## Architecture

The module is designed with clean separation of concerns:

- **config.rs**: Configuration management and presets
- **task.rs**: Core task monitoring logic
- **error.rs**: Error types and handling
- **mod.rs**: Main API and manager implementation
- **examples.rs**: Usage examples and documentation

## Dependencies

- `tokio`: Async runtime and utilities
- `sysinfo`: System and process information
- `serde`: Serialization support
- `log`: Logging framework
- `thiserror`: Error handling

## License

This module is part of the larger application and follows the same licensing terms.
