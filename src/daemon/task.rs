use super::error::{<PERSON><PERSON><PERSON><PERSON>, Result};
use log;
use serde::{Deserialize, Serialize};
use sysinfo::{Pid, Process, ProcessRefreshKind, ProcessStatus, RefreshKind, System};
use std::sync::Arc;
use tokio::sync::Mutex;
use super::ResourceUsage;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum ProcThreshold {
    Cpu(f32),
    Memory(u64),
    Disk(u64),
    Network(u64),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ResourceLimitAct {
    Warn,
    Kill,
    Limit,
}

#[derive(Debug)]
pub enum ResourceLimitError {
    CpuExceeded { current: f32, limit: f32 },
    MemoryExceeded { current: u64, limit: u64 },
    DiskExceeded { current: u64, limit: u64 },
    NetworkExceeded { current: u64, limit: u64 },
}

pub type ResourceLimitResult = std::result::Result<(), ResourceLimitError>;

#[derive(Debug, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct ResourceLimit {
    pub(crate) threshold: ProcThreshold,
    pub(crate) action: ResourceLimitAct,
}

impl ResourceLimit {
    /// Check if the process exceeds the resource limit
    pub fn check(&self, proc: &Process) -> ResourceLimitResult {
        use super::CPUS;
        match &self.threshold {
            ProcThreshold::Cpu(threshold) => {
                let cpu_usage = proc.cpu_usage();
                if cpu_usage / *CPUS as f32 > *threshold {
                    log::warn!(
                        "Process {} CPU usage {:.2}% exceeds limit {:.2}%",
                        proc.pid(),
                        cpu_usage,
                        threshold
                    );
                    Err(ResourceLimitError::CpuExceeded {
                        current: cpu_usage,
                        limit: *threshold,
                    })
                } else {
                    Ok(())
                }
            }
            ProcThreshold::Memory(threshold) => {
                let memory_usage = proc.memory();
                if memory_usage > *threshold {
                    log::warn!(
                        "Process {} memory usage {} bytes exceeds limit {} bytes",
                        proc.pid(),
                        memory_usage,
                        threshold
                    );
                    Err(ResourceLimitError::MemoryExceeded {
                        current: memory_usage,
                        limit: *threshold,
                    })
                } else {
                    Ok(())
                }
            }
            ProcThreshold::Disk(_threshold) => {
                // Disk I/O monitoring would require additional system calls
                // For now, we'll implement a placeholder that always passes
                log::debug!(
                    "Disk I/O monitoring not yet implemented for process {}",
                    proc.pid()
                );
                Ok(())
            }
            ProcThreshold::Network(_threshold) => {
                // Network I/O monitoring would require additional system calls
                // For now, we'll implement a placeholder that always passes
                log::debug!(
                    "Network I/O monitoring not yet implemented for process {}",
                    proc.pid()
                );
                Ok(())
            }
        }
    }
}
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DaemonConfig {
    pub(crate) limits: Vec<ResourceLimit>,
    pub(crate) interval: u64,
}
impl Default for DaemonConfig {
    fn default() -> Self {
        Self {
            limits: Vec::new(),
            interval: 30,
        }
    }
}

// pub trait Limit {
//     fn limit(&self);
// }

#[derive(Debug, Clone, PartialEq)]
pub enum State {
    Running,
    Stopped,
    Ready,
}

#[derive(Debug)]
pub enum TaskControl {
    ChangeState(State),
    ChangeConfig(DaemonConfig),
    Check,
}
pub struct Task {
    state: State,
    pid: Pid,
    sys: System,
    config: DaemonConfig,
    ctl_rx: tokio::sync::mpsc::Receiver<TaskControl>,
    resource_usage: Arc<Mutex<ResourceUsage>>,
}

impl Task {
    /// Create a new task for monitoring a process
    pub fn new(
        pid: u32,
        config: Option<DaemonConfig>,
        ctl_rx: tokio::sync::mpsc::Receiver<TaskControl>,
        resource_usage: Arc<Mutex<ResourceUsage>>,
    ) -> Self {
        log::info!("Created new daemon task for process {} ", pid);
        let sys = System::new_with_specifics(RefreshKind::nothing().with_processes(ProcessRefreshKind::everything()));
        Self {
            state: State::Ready,
            pid: Pid::from_u32(pid),
            sys,
            resource_usage,
            config: config.unwrap_or_default(),
            ctl_rx,
        }
    }

    /// Get the current state of the task
    pub fn state(&self) -> &State {
        &self.state
    }

    /// Get the process ID being monitored
    pub fn pid(&self) -> u32 {
        self.pid.as_u32()
    }

    pub fn get_proc(&self) -> Result<&Process> {
        self.sys.process(self.pid).ok_or_else(|| DaemonError::ProcessError(format!("Process {} not found", self.pid())))
    }

    /// Check if the process is still alive
    pub fn is_process_alive(&self) -> Result<bool> {
        let proc: &Process = self.get_proc()?;
        if proc.exists() {
            return Ok(false);
        }
        if proc.status() == ProcessStatus::Dead || proc.status() == ProcessStatus::Zombie {
            return Ok(false);
        }
        Ok(true)
    }
    pub async fn run(&mut self) -> Result<()> {
        loop {
            if self.state == State::Stopped {
                return Ok(());
            }

            self.sys
                .refresh_processes(sysinfo::ProcessesToUpdate::Some(&[self.pid]), false);
            self.is_process_alive()?;
            for limit in &self.config.limits {
                // 利用错误类型信息记录错误，简化错误处理流程
                if let Err(error) = limit.check(self.get_proc()?) {
                    // Log detailed error information
                    log::warn!("Process {} exceeded limit: {:?}", self.pid, error);
                    log::warn!("Taking action: {:?}", limit.action);

                    match limit.action {
                        ResourceLimitAct::Kill => {
                            log::warn!("Killing process {}", self.pid);
                            self.get_proc()?.kill();
                            return Err(DaemonError::CheckError(format!(
                                "Process {} exceeded limit: {:?}",
                                self.pid, error
                            )));
                        }
                        ResourceLimitAct::Warn => {
                            // Warning already logged above
                        }
                        ResourceLimitAct::Limit => {
                            // TODO: Implement resource limiting (e.g., CPU throttling)
                            log::info!(
                                "Resource limiting not yet implemented for process {}",
                                self.pid
                            );
                        }
                    };
                }
            }

            // Wait for control messages with timeout
            let _timeout_result = tokio::time::timeout(
                tokio::time::Duration::from_secs(self.config.interval),
                async {
                    match self.ctl_rx.recv().await {
                        Some(ctl) => match ctl {
                            TaskControl::ChangeState(state) => {
                                log::info!("Changing task state to {:?}", state);
                                self.state = state;
                            }
                            TaskControl::ChangeConfig(config) => {
                                log::info!("Updating task configuration");
                                self.config = config;
                            }
                            _ => {
                                unimplemented!("TODO: check")
                            }
                        },
                        None => {
                            log::debug!("Control channel closed");
                        }
                    }
                },
            )
            .await;
        }
    }
}
