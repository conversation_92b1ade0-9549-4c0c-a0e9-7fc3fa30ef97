mod config;
mod error;
mod task;

pub use self::config::{presets, ManagerConfig};
pub use self::error::{DaemonError, Result};
pub use self::task::{
    DaemonConfig, ProcThreshold, ResourceLimit, ResourceLimitAct, State, Task, TaskControl,
};

use log;
use watchmend::common::handle;
use std::collections::HashMap;
use std::sync::Arc;
use sysinfo::System;
use tokio::sync::Mutex;
use tokio::sync::mpsc;
use tokio::task::JoinHandle;

lazy_static::lazy_static! {
    static ref SYS: sysinfo::System = {
        let mut sys = sysinfo::System::new_all();
        sys.refresh_cpu_specifics(sysinfo::CpuRefreshKind::everything());
        sys
    };

    static ref CPUS: usize = SYS.cpus().len();
}

#[derive(Default)]
pub struct ResourceUsage {
    /// cpu usage range is 0~100%
    pub cpu_usage: f32,
    /// memory usage in bytes
    pub mem_usage: u64,
}

/// Represents a managed task process with lifecycle control
pub struct TaskMan {
    /// Process ID being monitored
    pub pid: u32,
    /// Task configuration
    pub config: DaemonConfig,
    /// Restart attempt counter
    pub restart_attempts: u32,
    /// Resource usage information
    pub resource_usage: Arc<Mutex<ResourceUsage>>,
    ctl_tx: mpsc::Sender<TaskControl>,
}

impl TaskMan {
    /// Create a new TaskProcess
    pub fn new(
        pid: u32,
        config: Option<DaemonConfig>,
    ) -> Self {
        log::info!(
            "Creating new TaskProcess for PID {}",
            pid,
        );

        Self {
            pid,
            config: config.unwrap_or_default(),
            restart_attempts: 0,
            resource_usage: Arc::new(Mutex::new(ResourceUsage::default())),
        }
    }

    /// Start the task monitoring
    pub fn start(&mut self, ctl_rx: mpsc::Receiver<TaskControl>) -> Result<JoinHandle<()>> {

        let pid = self.pid;
        let config = self.config.clone();
        let resource_usage = self.resource_usage.clone();
        let handle = tokio::spawn(async move {
            log::info!("Starting task monitoring for '{}'", pid);
            
            // Create the task inside the async context
            let mut task = Task::new(pid, Some(config), ctl_rx, resource_usage);
            task.run().await;

            log::info!("Task monitoring ended for '{}'", pid);
        });

        log::info!("Successfully started task '{}'", pid);
        Ok(handle)
    }

    pub fn update_config(&mut self, new_config: DaemonConfig) -> Result<()> {
        self.config = new_config;
        log::info!("Updated configuration for task '{}'", self.pid);
        Ok(())
    }

    /// Increment restart attempts
    pub fn increment_restart_attempts(&mut self) {
        self.restart_attempts += 1;
    }

    /// Reset restart attempts
    pub fn reset_restart_attempts(&mut self) {
        self.restart_attempts = 0;
    }
}

/// Task status information
#[derive(Debug, Clone)]
pub struct TaskStatus {
    pub name: String,
    pub pid: u32,
    pub state: State,
    pub restart_attempts: u32,
    pub is_alive: bool,
}

/// Daemon manager for handling multiple task processes
pub struct DaemonManager {
    tasks: HashMap<String, TaskMan>,
    handles: HashMap<String, JoinHandle<()>>,
    config: ManagerConfig,
}

impl DaemonManager {
    /// Create a new daemon manager
    pub fn new() -> Self {
        log::info!("Creating new DaemonManager");
        Self {
            tasks: HashMap::new(),
            handles: HashMap::new(),
            config: ManagerConfig::new(),
        }
    }

    /// Create a new daemon manager with custom configuration
    pub fn with_config(config: ManagerConfig) -> Result<Self> {
        config.validate().map_err(|e| DaemonError::CreateError(e))?;

        log::info!("Creating DaemonManager with custom config");
        Ok(Self {
            tasks: HashMap::new(),
            handles: HashMap::new(),
            config,
        })
    }

    /// Get a reference to a task
    fn get_task(&self, name: &str) -> Result<&TaskMan> {
        self.tasks
            .get(name)
            .ok_or_else(|| DaemonError::GetError(format!("Task '{}' not found", name)))
    }

    /// Get a mutable reference to a task
    fn get_task_mut(&mut self, name: &str) -> Result<&mut TaskMan> {
        self.tasks
            .get_mut(name)
            .ok_or_else(|| DaemonError::GetError(format!("Task '{}' not found", name)))
    }

    fn task_is_running(&self, name: &str) -> bool {
        self.handles.get(name).map_or(false, |h| !h.join_handle.is_finished())
    }

    /// Add a new task to the manager
    pub fn add_task(&mut self, name: String, pid: u32, config: Option<DaemonConfig>) -> Result<()> {
        if self.tasks.contains_key(&name) {
            return Err(DaemonError::CreateError(format!(
                "Task '{}' already exists",
                name
            )));
        }

        if self.tasks.len() >= self.config.max_tasks {
            return Err(DaemonError::CreateError(format!(
                "Maximum number of tasks ({}) reached",
                self.config.max_tasks
            )));
        }

        let task_config = config.unwrap_or_else(|| self.config.default_task_config.clone());

        let task = TaskMan::new(pid, Some(task_config));
        self.tasks.insert(name.clone(), task);

        log::info!("Added task '{}' for PID {}", name, pid);
        Ok(())
    }

    /// Start monitoring a task
    pub fn start_task(&mut self, name: &str) -> Result<()> {
        let channel_size = self.config.channel_size * size_of::<TaskControl>();
        let task = self.get_task_mut(name)?;
        let (ctl_tx, ctl_rx) = mpsc::channel(channel_size);
        let jh = task.start(ctl_rx)?;
        self.handles.insert(name.to_string(), jh);
        log::info!("Started task '{}'", name);
        Ok(())
    }

    /// Stop monitoring a task
    pub async fn stop_task(&mut self, name: &str) -> Result<()> {
        let handle = self.handles.remove(name).ok_or_else(|| DaemonError::StopError(format!("Task '{}' not found", name)))?;
        handle.ctl_tx.send(TaskControl::ChangeState(State::Stopped)).await;
        let jh = handle.join_handle;
        jh.abort();
        log::info!("Stopped task '{}'", name);
        Ok(())
    }

    /// Remove a task from the manager
    pub async fn remove_task(&mut self, name: &str) -> Result<()> {
        // Stop the task first if it's running
        if self.task_is_running(name) {
            self.stop_task(name).await?;
        }

        self.tasks
            .remove(name)
            .ok_or_else(|| DaemonError::DeleteError(format!("Task '{}' not found", name)))?;

        log::info!("Removed task '{}'", name);
        Ok(())
    }

    /// Update the global configuration
    pub fn update_config(&mut self, new_config: ManagerConfig) -> Result<()> {
        new_config
            .validate()
            .map_err(|e| DaemonError::ConfigureError(e))?;
        self.config = new_config;
        log::info!("Updated daemon manager configuration");
        Ok(())
    }

    /// Update configuration for a specific task
    pub fn update_task_config(&mut self, name: &str, new_config: DaemonConfig) -> Result<()> {
        let task = self.get_task_mut(name)?;
        task.update_config(new_config)?;
        log::info!("Updated configuration for task '{}'", name);
        Ok(())
    }

    /// Get status of all tasks
    pub fn list_tasks(&self) -> Vec<TaskStatus> {
        self.tasks.values().map(|task| task.status()).collect()
    }

    /// Get status of a specific task
    pub fn get_task_status(&self, name: &str) -> Result<TaskStatus> {
        let task = self.get_task(name)?;
        Ok(task.status())
    }

    /// Restart a task
    pub async fn restart_task(&mut self, name: &str) -> Result<()> {
        log::info!("Restarting task '{}'", name);

        // Stop the task if it's running
        if let Ok(task) = self.get_task(name) {
            if task.is_running() {
                self.stop_task(name).await?;
            }
        }

        // Start the task again
        self.start_task(name)?;

        // Reset restart attempts on successful restart
        let task = self.get_task_mut(name)?;
        task.reset_restart_attempts();

        log::info!("Successfully restarted task '{}'", name);
        Ok(())
    }

    /// Check and restart failed tasks if auto-restart is enabled
    pub async fn check_and_restart_failed_tasks(&mut self) -> Result<()> {
        if !self.config.auto_restart {
            return Ok(());
        }

        let failed_tasks: Vec<String> = self
            .tasks
            .iter()
            .filter(|(_, task)| {
                task.state == State::Running
                    && !task.is_running()
                    && task.restart_attempts < self.config.max_restart_attempts
            })
            .map(|(name, _)| name.clone())
            .collect();

        for task_name in failed_tasks {
            log::warn!("Detected failed task '{}', attempting restart", task_name);

            // Increment restart attempts
            if let Ok(task) = self.get_task_mut(&task_name) {
                task.increment_restart_attempts();
            }

            // Attempt restart
            if let Err(e) = self.restart_task(&task_name).await {
                log::error!("Failed to restart task '{}': {}", task_name, e);
            }
        }

        Ok(())
    }

    /// Get the current configuration
    pub fn get_config(&self) -> &ManagerConfig {
        &self.config
    }

    /// Get the number of active tasks
    pub fn task_count(&self) -> usize {
        self.tasks.len()
    }

    /// Get the number of running tasks
    pub fn running_task_count(&self) -> usize {
        self.handles.iter().filter(|(_, handle)| !handle.join_handle.is_finished()).count()
    }

    /// Stop all running tasks
    pub async fn stop_all_tasks(&mut self) -> Result<()> {
        let task_names: Vec<String> = self
            .tasks
            .iter()
            .filter(|(_, task)| task.is_running())
            .map(|(name, _)| name.clone())
            .collect();

        for name in task_names {
            if let Err(e) = self.stop_task(&name).await {
                log::error!("Failed to stop task '{}': {}", name, e);
            }
        }

        log::info!("Stopped all running tasks");
        Ok(())
    }

    /// Start all ready tasks
    pub fn start_all_tasks(&mut self) -> Result<()> {
        let task_names: Vec<String> = self
            .tasks
            .iter()
            .filter(|(_, task)| task.state == State::Ready)
            .map(|(name, _)| name.clone())
            .collect();

        for name in task_names {
            if let Err(e) = self.start_task(&name) {
                log::error!("Failed to start task '{}': {}", name, e);
            }
        }

        log::info!("Started all ready tasks");
        Ok(())
    }

    /// Get tasks by state
    pub fn get_tasks_by_state(&self, state: State) -> Vec<TaskStatus> {
        self.tasks
            .values()
            .filter(|task| task.state == state)
            .map(|task| task.status())
            .collect()
    }

    /// Check if a task exists
    pub fn has_task(&self, name: &str) -> bool {
        self.tasks.contains_key(name)
    }

    /// Get task names
    pub fn task_names(&self) -> Vec<String> {
        self.tasks.keys().cloned().collect()
    }

    /// Cleanup finished tasks
    pub fn cleanup_finished_tasks(&mut self) -> usize {
        let finished_tasks: Vec<String> = self
            .tasks
            .iter()
            .filter(|(_, task)| {
                task.state == State::Stopped || (task.state == State::Running && !self.task_is_running(name))
            })
            .map(|(name, _)| name.clone())
            .collect();

        let count = finished_tasks.len();
        for name in finished_tasks {
            self.tasks.remove(&name);
            log::debug!("Cleaned up finished task '{}'", name);
        }

        if count > 0 {
            log::info!("Cleaned up {} finished tasks", count);
        }

        count
    }

}

/// Convenience functions for creating daemon managers and common configurations
pub mod builder {
    use super::*;
    use std::sync::Arc;
    use sysinfo::System;

    /// Builder for creating DaemonManager instances
    pub struct DaemonManagerBuilder {
        config: ManagerConfig,
        system: Option<Arc<System>>,
    }

    impl DaemonManagerBuilder {
        /// Create a new builder with default configuration
        pub fn new() -> Self {
            Self {
                config: ManagerConfig::default(),
                system: None,
            }
        }

        /// Set the manager configuration
        pub fn with_config(mut self, config: ManagerConfig) -> Self {
            self.config = config;
            self
        }

        /// Set a custom system instance
        pub fn with_system(mut self, system: Arc<System>) -> Self {
            self.system = Some(system);
            self
        }

        /// Set the global monitoring interval
        pub fn with_interval(mut self, interval_secs: u64) -> Self {
            self.config.global_interval = interval_secs;
            self
        }

        /// Set maximum number of tasks
        pub fn with_max_tasks(mut self, max_tasks: usize) -> Self {
            self.config.max_tasks = max_tasks;
            self
        }

        /// Enable verbose logging
        pub fn with_verbose_logging(mut self) -> Self {
            self.config.verbose_logging = true;
            self
        }

        /// Configure auto-restart behavior
        pub fn with_auto_restart(mut self, max_attempts: u32) -> Self {
            self.config.auto_restart = true;
            self.config.max_restart_attempts = max_attempts;
            self
        }

        /// Build the DaemonManager
        pub fn build(self) -> Result<DaemonManager> {
            let system = self.system.unwrap_or_else(|| {
                let sys = System::new_all();
                Arc::new(sys)
            });

            DaemonManager::with_config(system, self.config)
        }
    }

    impl Default for DaemonManagerBuilder {
        fn default() -> Self {
            Self::new()
        }
    }
}

/// Convenience functions for common daemon operations
pub mod utils {
    use super::*;

    /// Create a daemon manager with default settings
    pub fn create_default_manager() -> Result<DaemonManager> {
        builder::DaemonManagerBuilder::new().build()
    }

    /// Create a daemon manager optimized for server monitoring
    pub fn create_server_manager() -> Result<DaemonManager> {
        let config = ManagerConfig::default()
            .with_interval(30)
            .with_max_tasks(50)
            .with_verbose_logging(true)
            .with_auto_restart(true, 3)
            .with_default_task_config(presets::server_config());

        builder::DaemonManagerBuilder::new()
            .with_config(config)
            .build()
    }

    /// Create a daemon manager optimized for background process monitoring
    pub fn create_background_manager() -> Result<DaemonManager> {
        let config = ManagerConfig::default()
            .with_interval(60)
            .with_max_tasks(100)
            .with_auto_restart(true, 5)
            .with_default_task_config(presets::background_config());

        builder::DaemonManagerBuilder::new()
            .with_config(config)
            .build()
    }

    /// Create a daemon manager for critical process monitoring
    pub fn create_critical_manager() -> Result<DaemonManager> {
        let config = ManagerConfig::default()
            .with_interval(15)
            .with_max_tasks(20)
            .with_verbose_logging(true)
            .with_auto_restart(true, 1)
            .with_default_task_config(presets::critical_config());

        builder::DaemonManagerBuilder::new()
            .with_config(config)
            .build()
    }

    /// Quick function to monitor a single process
    pub fn monitor_process(
        pid: i32,
        name: String,
        config: Option<DaemonConfig>,
    ) -> Result<DaemonManager> {
        let mut manager = create_default_manager()?;
        manager.add_task(name.clone(), pid, config)?;
        manager.start_task(&name)?;
        Ok(manager)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Arc;
    use sysinfo::System;

    fn create_test_system() -> Arc<System> {
        Arc::new(System::new_all())
    }

    #[test]
    fn test_manager_config_creation() {
        let config = ManagerConfig::new();
        assert_eq!(config.global_interval, 10);
        assert_eq!(config.max_tasks, 100);
        assert!(config.auto_restart);
    }

    #[test]
    fn test_daemon_manager_creation() {
        let sys = create_test_system();
        let manager = DaemonManager::new(sys);

        assert_eq!(manager.task_count(), 0);
        assert_eq!(manager.running_task_count(), 0);
    }

    #[test]
    fn test_add_task_to_manager() {
        let sys = create_test_system();
        let mut manager = DaemonManager::new(sys);
        let current_pid = std::process::id();

        let result = manager.add_task("test_task".to_string(), current_pid, None);

        assert!(result.is_ok());
        assert_eq!(manager.task_count(), 1);
        assert!(manager.has_task("test_task"));
    }

    #[test]
    fn test_preset_configurations() {
        let server_config = presets::server_config();
        assert!(!server_config.limits.is_empty());
        assert_eq!(server_config.interval, 30);

        let background_config = presets::background_config();
        assert!(!background_config.limits.is_empty());
        assert_eq!(background_config.interval, 60);
    }

    #[test]
    fn test_builder_pattern() {
        let result = builder::DaemonManagerBuilder::new()
            .with_interval(25)
            .with_max_tasks(15)
            .with_verbose_logging()
            .build();

        assert!(result.is_ok());
        let manager = result.unwrap();
        assert_eq!(manager.get_config().global_interval, 25);
        assert_eq!(manager.get_config().max_tasks, 15);
        assert!(manager.get_config().verbose_logging);
    }

    #[test]
    fn test_utils_functions() {
        let result1 = utils::create_default_manager();
        assert!(result1.is_ok());

        let result2 = utils::create_server_manager();
        assert!(result2.is_ok());

        let result3 = utils::create_background_manager();
        assert!(result3.is_ok());

        let result4 = utils::create_critical_manager();
        assert!(result4.is_ok());
    }
}
