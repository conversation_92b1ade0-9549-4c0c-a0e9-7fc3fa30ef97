mod app;
mod daemon;
mod dbus;
mod package;

use app::{AppManager, AppManagerConfig, AppCommand, AppSubCommand, AppManagerDBus};
use std::sync::Arc;
use tokio::sync::RwLock;
use log::{info, error, warn};

/// Main application entry point using the new app module
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Info)
        .init();

    info!("Starting Application Manager (New Implementation)");

    // Parse command line arguments
    let cmd = AppCommand::parse_args();

    // Initialize configuration
    let mut config = AppManagerConfig::default();
    
    if cmd.verbose {
        env_logger::Builder::from_default_env()
            .filter_level(log::LevelFilter::Debug)
            .init();
        config = config.with_verbose(true);
    }

    if let Some(config_file) = cmd.config {
        // TODO: Load configuration from file
        warn!("Configuration file loading not implemented: {}", config_file.display());
    }

    // Check if running as daemon or handling single command
    match &cmd.command {
        AppSubCommand::List { installed, running, detailed } => {
            handle_list_command(*installed, *running, *detailed, config).await?;
        }
        AppSubCommand::Start { name, debug, args } => {
            handle_start_command(name, *debug, args.as_deref(), config).await?;
        }
        AppSubCommand::Stop { name, force } => {
            handle_stop_command(name, *force, config).await?;
        }
        AppSubCommand::Enable { name } => {
            handle_enable_command(name, config).await?;
        }
        AppSubCommand::Disable { name } => {
            handle_disable_command(name, config).await?;
        }
        AppSubCommand::Ver { name } => {
            handle_version_command(name, config).await?;
        }
        AppSubCommand::State { name, format } => {
            handle_state_command(name, format, config).await?;
        }
        AppSubCommand::Install { dest, packages, force: _ } => {
            handle_install_command(packages, dest.clone(), config).await?;
        }
        AppSubCommand::Remove { packages, auto_remove: _ } => {
            handle_remove_command(packages, config).await?;
        }
        AppSubCommand::Update => {
            handle_update_command(config).await?;
        }
        AppSubCommand::Upgrade { packages } => {
            handle_upgrade_command(packages, config).await?;
        }
        AppSubCommand::ListUpgradable => {
            handle_list_upgradable_command(config).await?;
        }
        AppSubCommand::Daemon { name, command } => {
            handle_daemon_command(name, command, config).await?;
        }
        _ => {
            // For other commands, start the daemon mode
            start_daemon_mode(config).await?;
        }
    }

    Ok(())
}

/// Handle list command
async fn handle_list_command(
    installed: bool,
    running: bool,
    detailed: bool,
    config: AppManagerConfig,
) -> Result<(), Box<dyn std::error::Error>> {
    let mut manager = AppManager::new(config)?;
    manager.initialize().await?;

    let apps = manager.list_applications(installed, running).await?;

    if detailed {
        for app in apps {
            println!("Application: {}", app.name);
            println!("  Version: {}", app.version);
            println!("  State: {}", app.state);
            println!("  Auto-start: {}", app.auto_start);
            println!("  Daemon: {}", app.daemon_state);
            if let Some(pid) = app.resource_usage.pid {
                println!("  PID: {}", pid);
                println!("  CPU: {:.1}%", app.resource_usage.cpu_percent);
                println!("  Memory: {} bytes", app.resource_usage.memory_bytes);
                println!("  Uptime: {} seconds", app.resource_usage.uptime_seconds);
            }
            println!();
        }
    } else {
        println!("{:<20}{:<10}{:<10}{}", "Appname", "Stage", "Status", "Running");
        for app in apps {
            let stage = app.get_stage();
            let status = if app.is_auto_start_enabled() { "enabled" } else { "disabled" };
            let running = if app.is_running() { "True" } else { "False" };
            println!("{:<20}S{:<9}{:<10}{}", app.name, stage, status, running);
        }
    }

    Ok(())
}

/// Handle start command
async fn handle_start_command(
    name: &str,
    debug: bool,
    _args: Option<&str>,
    config: AppManagerConfig,
) -> Result<(), Box<dyn std::error::Error>> {
    let mut manager = AppManager::new(config)?;
    manager.initialize().await?;

    if name == "all" {
        manager.start_auto_start_applications().await?;
        println!("Started all auto-start applications");
    } else {
        manager.start_application(name, debug).await?;
        println!("Started application: {}", name);
    }

    Ok(())
}

/// Handle stop command
async fn handle_stop_command(name: &str, force: bool, config: AppManagerConfig) -> Result<(), Box<dyn std::error::Error>> {
    let mut manager = AppManager::new(config)?;
    manager.initialize().await?;

    if name == "all" {
        manager.stop_all_applications(force).await?;
        println!("Stopped all applications");
    } else {
        manager.stop_application(name, force).await?;
        println!("Stopped application: {}", name);
    }

    Ok(())
}

/// Handle enable command
async fn handle_enable_command(name: &str, config: AppManagerConfig) -> Result<(), Box<dyn std::error::Error>> {
    let mut manager = AppManager::new(config)?;
    manager.initialize().await?;

    manager.enable_auto_start(name).await?;
    println!("Enabled auto-start for: {}", name);

    Ok(())
}

/// Handle disable command
async fn handle_disable_command(name: &str, config: AppManagerConfig) -> Result<(), Box<dyn std::error::Error>> {
    let mut manager = AppManager::new(config)?;
    manager.initialize().await?;

    manager.disable_auto_start(name).await?;
    println!("Disabled auto-start for: {}", name);

    Ok(())
}

/// Handle version command
async fn handle_version_command(name: &str, config: AppManagerConfig) -> Result<(), Box<dyn std::error::Error>> {
    let manager = AppManager::new(config)?;
    let version = manager.get_application_version(name).await?;
    println!("{}", version);
    Ok(())
}

/// Handle state command
async fn handle_state_command(name: &str, format: &str, config: AppManagerConfig) -> Result<(), Box<dyn std::error::Error>> {
    let manager = AppManager::new(config)?;
    let app_info = manager.get_application_state(name).await?;

    match format {
        "json" => {
            let json = serde_json::to_string_pretty(&app_info)?;
            println!("{}", json);
        }
        "yaml" => {
            // TODO: Implement YAML output
            println!("YAML format not implemented");
        }
        "table" | _ => {
            println!("Application: {}", app_info.name);
            println!("State: {}", app_info.state);
            println!("Auto-start: {}", app_info.auto_start);
            println!("Daemon: {}", app_info.daemon_state);
            if let Some(pid) = app_info.resource_usage.pid {
                println!("PID: {}", pid);
                println!("CPU: {:.1}%", app_info.resource_usage.cpu_percent);
                println!("Memory: {} bytes", app_info.resource_usage.memory_bytes);
            }
        }
    }

    Ok(())
}

/// Handle daemon command
async fn handle_daemon_command(
    name: &str,
    command: &app::commands::DaemonCommand,
    config: AppManagerConfig,
) -> Result<(), Box<dyn std::error::Error>> {
    let mut manager = AppManager::new(config)?;
    manager.initialize().await?;

    use app::commands::DaemonCommand;
    use app::{DaemonState};

    match command {
        DaemonCommand::Start => {
            manager.set_daemon_state(name, DaemonState::Enabled).await?;
            println!("Started daemon monitoring for: {}", name);
        }
        DaemonCommand::Stop => {
            manager.set_daemon_state(name, DaemonState::Disabled).await?;
            println!("Stopped daemon monitoring for: {}", name);
        }
        DaemonCommand::Suspend => {
            manager.set_daemon_state(name, DaemonState::Suspended).await?;
            println!("Suspended daemon monitoring for: {}", name);
        }
        DaemonCommand::Resume => {
            manager.set_daemon_state(name, DaemonState::Enabled).await?;
            println!("Resumed daemon monitoring for: {}", name);
        }
        DaemonCommand::Status => {
            let app_info = manager.get_application_state(name).await?;
            println!("Daemon status for {}: {}", name, app_info.daemon_state);
        }
        DaemonCommand::Limit { cpu, memory, blkio } => {
            // TODO: Implement resource limits
            println!("Setting resource limits for {} (not implemented)", name);
            if let Some(cpu) = cpu {
                println!("  CPU limit: {}%", cpu);
            }
            if let Some(memory) = memory {
                println!("  Memory limit: {} MB", memory);
            }
            if let Some(blkio) = blkio {
                println!("  Block I/O limit: {} MB/s", blkio);
            }
        }
        DaemonCommand::Threshold { cpu, memory, action } => {
            // TODO: Implement resource thresholds
            println!("Setting resource thresholds for {} (not implemented)", name);
            if let Some(cpu) = cpu {
                println!("  CPU threshold: {}%", cpu);
            }
            if let Some(memory) = memory {
                println!("  Memory threshold: {}%", memory);
            }
            println!("  Action: {}", action);
        }
    }

    Ok(())
}

/// Handle install command
async fn handle_install_command(
    packages: &[String],
    dest: Option<std::path::PathBuf>,
    config: AppManagerConfig,
) -> Result<(), Box<dyn std::error::Error>> {
    let mut manager = AppManager::new(config)?;
    manager.initialize().await?;

    match manager.install_packages(packages.to_vec(), dest).await {
        Ok(()) => println!("Installed packages: {:?}", packages),
        Err(e) => {
            error!("Failed to install packages: {}", e);
            return Err(e.into());
        }
    }

    Ok(())
}

/// Handle remove command
async fn handle_remove_command(packages: &[String], config: AppManagerConfig) -> Result<(), Box<dyn std::error::Error>> {
    let mut manager = AppManager::new(config)?;
    manager.initialize().await?;

    match manager.remove_packages(packages.to_vec()).await {
        Ok(()) => println!("Removed packages: {:?}", packages),
        Err(e) => {
            error!("Failed to remove packages: {}", e);
            return Err(e.into());
        }
    }

    Ok(())
}

/// Handle update command
async fn handle_update_command(config: AppManagerConfig) -> Result<(), Box<dyn std::error::Error>> {
    let manager = AppManager::new(config)?;
    
    match manager.update_package_index().await {
        Ok(packages) => println!("Updated package index, found {} packages", packages.len()),
        Err(e) => {
            error!("Failed to update package index: {}", e);
            return Err(e.into());
        }
    }
    
    Ok(())
}

/// Handle upgrade command
async fn handle_upgrade_command(packages: &[String], config: AppManagerConfig) -> Result<(), Box<dyn std::error::Error>> {
    let mut manager = AppManager::new(config)?;
    manager.initialize().await?;

    match manager.upgrade_packages(packages.to_vec()).await {
        Ok(()) => {
            if packages.is_empty() {
                println!("Upgraded all packages");
            } else {
                println!("Upgraded packages: {:?}", packages);
            }
        }
        Err(e) => {
            error!("Failed to upgrade packages: {}", e);
            return Err(e.into());
        }
    }

    Ok(())
}

/// Handle list upgradable command
async fn handle_list_upgradable_command(config: AppManagerConfig) -> Result<(), Box<dyn std::error::Error>> {
    let manager = AppManager::new(config)?;
    
    match manager.list_upgradable_packages().await {
        Ok(packages) => {
            if packages.is_empty() {
                println!("No packages available for upgrade");
            } else {
                println!("Upgradable packages:");
                for package in packages {
                    println!("  {}", package);
                }
            }
        }
        Err(e) => {
            error!("Failed to list upgradable packages: {}", e);
            return Err(e.into());
        }
    }

    Ok(())
}

/// Start daemon mode with D-Bus interface
async fn start_daemon_mode(config: AppManagerConfig) -> Result<(), Box<dyn std::error::Error>> {
    info!("Starting daemon mode");

    // Create application manager
    let mut manager = AppManager::new(config)?;
    manager.initialize().await?;

    let manager_arc = Arc::new(RwLock::new(manager));

    // Start D-Bus service in background
    let dbus_manager = manager_arc.clone();
    let dbus_handle = tokio::spawn(async move {
        if let Err(e) = AppManagerDBus::start_service(dbus_manager).await {
            error!("D-Bus service failed: {}", e);
        }
    });

    // Run main manager loop
    let main_manager = manager_arc.clone();
    let main_handle = tokio::spawn(async move {
        let mut mgr = main_manager.write().await;
        if let Err(e) = mgr.run().await {
            error!("Manager main loop failed: {}", e);
        }
    });

    // Wait for either task to complete
    tokio::select! {
        _ = dbus_handle => {
            warn!("D-Bus service ended");
        }
        _ = main_handle => {
            warn!("Manager main loop ended");
        }
    }

    info!("Daemon mode ended");
    Ok(())
}
