use super::*;

/// Example usage of the package management system
#[cfg(opkg)]
pub fn example_usage() -> Result<()> {
    // Create an opkg manager
    let opkg = Opkg::new().with_verbosity(2).with_dest("root".to_string());

    // Update package index
    println!("Updating package index...");
    let packages = opkg.update_index()?;
    println!("Found {} packages", packages.len());

    // List all available packages
    println!("\nListing available packages:");
    let available = opkg.list_packages()?;
    for package in available.iter().take(5) {
        println!(
            "  {} - {}",
            package.name,
            package.version.as_deref().unwrap_or("unknown")
        );
    }

    // List installed packages
    println!("\nListing installed packages:");
    let installed = opkg.list_installed()?;
    for package in installed.iter().take(5) {
        println!(
            "  {} - {}",
            package.name,
            package.version.as_deref().unwrap_or("unknown")
        );
    }

    // Search for packages
    println!("\nSearching for packages containing 'test':");
    let search_results = opkg.search_package("*test*")?;
    for package in search_results {
        println!(
            "  {} - {}",
            package.name,
            package.version.as_deref().unwrap_or("unknown")
        );
    }

    // Get package info
    if let Ok(info) = opkg.info_package("test") {
        println!("\nPackage info for 'test':");
        println!("{}", info);
    }

    // Install a package (commented out for safety)
    // opkg.install_package(vec!["example"], None)?;

    // Remove a package (commented out for safety)
    // opkg.remove_package(vec!["example"])?;

    Ok(())
}

/// Example of configuration management
pub fn config_example() -> Result<()> {
    // Create a new configuration
    let mut config = Config::new();

    // Add destinations
    config.update_dest("root", "/")?;
    config.update_dest("ram", "/tmp")?;

    // Add options
    config.update_option("lists_dir", "/var/opkg-lists")?;
    config.update_option("overlay_root", "/overlay")?;

    // Add architectures
    config.update_arch("all", 100)?;
    config.update_arch("arm_cortex-a7", 200)?;
    config.update_arch("arm_cortex-a7_vfpv4", 300)?;
    config.update_arch("arm_cortex-a7_neon-vfpv4", 400)?;

    // Add sources
    config.update_src("local", CompressType::Gz, "file:///root/ipkgs")?;

    // Print configuration
    println!("Generated configuration:");
    println!("{}", config.to_string());

    // Save to file (commented out for safety)
    // config.write_to_file("/tmp/opkg.conf")?;

    Ok(())
}

/// Example of using convenience functions
#[cfg(opkg)]
pub fn convenience_example() -> Result<()> {
    // Use convenience functions
    println!("Using convenience functions:");

    // List available packages
    let available = list_available_packages()?;
    println!("Available packages: {}", available.len());

    // List installed packages
    let installed = list_installed_packages()?;
    println!("Installed packages: {}", installed.len());

    // Update index
    let updated = update_package_index()?;
    println!("Updated index with {} packages", updated.len());

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_config_example() {
        assert!(config_example().is_ok());
    }

    #[cfg(opkg)]
    #[test]
    fn test_opkg_creation_example() {
        let opkg = Opkg::new()
            .with_config_file("/etc/opkg/opkg.conf".to_string())
            .with_verbosity(3)
            .with_dest("root".to_string());

        assert_eq!(opkg.config_file, Some("/etc/opkg/opkg.conf".to_string()));
        assert_eq!(opkg.verbosity, 3);
        assert_eq!(opkg.dest, Some("root".to_string()));
    }
}
