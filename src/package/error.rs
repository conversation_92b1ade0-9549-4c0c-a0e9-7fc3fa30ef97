use thiserror::Error;

#[derive(Error, Debug)]
pub enum PackageError {
    #[error("Invalid package name: {0}")]
    InvalidPackageName(String),
    #[error("Package not found: {0}")]
    PackageNotFound(String),
    #[error("Failed to update package index: {0}")]
    UpdateIndexError(String),
    #[error("Failed to install package: {0}")]
    InstallError(String),
    #[error("Failed to remove package: {0}")]
    RemoveError(String),
    #[error("Failed to list packages: {0}")]
    ListError(String),
    #[error("Failed to search packages: {0}")]
    SearchError(String),
    #[error("Failed to get package info: {0}")]
    InfoError(String),
    #[error("Failed to get package status: {0}")]
    StatusError(String),
    #[error("Failed to get package files: {0}")]
    FilesError(String),
    #[error("Failed to download package: {0}")]
    DownloadError(String),
    #[error("Failed to configure package: {0}")]
    ConfigureError(String),
    #[error("Failed to upgrade packages: {0}")]
    UpgradeError(String),
    #[error("Failed to clean cache: {0}")]
    CleanError(String),
    #[error("Failed to get config: {0}")]
    GetConfigError(String),
    #[error("Failed to set config: {0}")]
    SetConfigError(String),
    #[error("Command execution failed: {0}")]
    CommandError(String),
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
    #[error("Parse error: {0}")]
    ParseError(String),
    #[error("Configuration error: {0}")]
    ConfigError(#[from] anyhow::Error),
}

pub type Result<T> = std::result::Result<T, PackageError>;
