# Package Management Module

这个模块提供了一个完整的包管理系统，专门为 opkg 包管理器设计，但具有可扩展的架构以支持其他包管理器。

## 功能特性

### 1. 配置管理 (`config.rs`)
- **完整的 opkg 配置解析**：支持 `dest`、`option`、`arch`、`src`、`src/gz` 指令
- **配置文件读写**：从文件读取配置并写回文件
- **动态配置管理**：通过 `SetConfig` trait 提供增删改查功能
- **序列化支持**：使用 serde 进行 JSON/其他格式序列化

### 2. 错误处理 (`error.rs`)
- **类型安全的错误处理**：使用 thiserror 定义详细的错误类型
- **全面的错误覆盖**：涵盖所有包管理操作的错误情况
- **错误链支持**：支持错误原因追踪

### 3. Opkg 实现 (`opkg.rs`)
- **完整的 opkg 命令包装**：支持所有主要的 opkg 操作
- **灵活的配置选项**：支持自定义配置文件、目标、详细级别等
- **智能包解析**：解析 opkg 输出为结构化数据
- **Builder 模式**：提供流畅的 API 来配置 opkg 实例

### 4. 通用接口 (`mod.rs`)
- **PackageManager Trait**：定义了包管理器的通用接口
- **便利函数**：提供简单易用的包管理操作
- **工厂函数**：自动创建适当的包管理器实例

## 支持的 Opkg 操作

### 包操作
- `update` - 更新包索引
- `install <pkgs>` - 安装包
- `remove <pkgs>` - 删除包
- `upgrade [pkgs]` - 升级包
- `configure <pkgs>` - 配置包
- `clean` - 清理缓存

### 信息查询
- `list` - 列出可用包
- `list-installed` - 列出已安装包
- `list-upgradable` - 列出可升级包
- `search <query>` - 搜索包
- `find <regexp>` - 按正则表达式查找包
- `info <pkg>` - 显示包信息
- `status <pkg>` - 显示包状态
- `files <pkg>` - 列出包文件
- `download <pkg>` - 下载包

### 配置选项
- `-f <conf_file>` - 指定配置文件
- `-d <dest>` - 指定安装目标
- `-V<level>` - 设置详细级别
- `-o <dir>` - 离线根目录
- `--cache-dir` - 缓存目录
- `-t <dir>` - 临时目录
- `-l <dir>` - 列表目录

## 使用示例

### 基本用法
```rust
use package::{Opkg, PackageManager};

// 创建 opkg 实例
let opkg = Opkg::new()
    .with_verbosity(2)
    .with_dest("root".to_string());

// 更新包索引
let packages = opkg.update_index()?;

// 列出已安装包
let installed = opkg.list_installed()?;

// 安装包
opkg.install_package(vec!["example"], None)?;

// 搜索包
let results = opkg.search_package("*test*")?;
```

### 配置管理
```rust
use package::{Config, SetConfig};

let mut config = Config::new();

// 添加配置
config.update_dest("root", "/")?;
config.update_option("lists_dir", "/var/opkg-lists")?;
config.update_arch("arm_cortex-a7", 200)?;
config.update_src("local", CompressType::Gz, "file:///root/ipkgs")?;

// 保存配置
config.write_to_file("/etc/opkg/opkg.conf")?;
```

### 便利函数
```rust
use package::*;

// 使用便利函数
let available = list_available_packages()?;
let installed = list_installed_packages()?;
install_packages(vec!["example"], None)?;
remove_packages(vec!["example"])?;
```

## 配置文件格式

支持标准的 opkg 配置文件格式：

```
dest root /
dest ram /tmp
option lists_dir /var/opkg-lists
option overlay_root /overlay

arch all 100
arch arm_cortex-a7 200
arch arm_cortex-a7_vfpv4 300
arch arm_cortex-a7_neon-vfpv4 400

src/gz local file:///root/ipkgs
```

## 测试

模块包含完整的测试套件：

```bash
cargo test package --lib
```

测试覆盖：
- 配置文件解析和生成
- opkg 命令构建和解析
- 错误处理
- 包管理操作

## 扩展性

该模块设计为可扩展的：
- `PackageManager` trait 可以被其他包管理器实现
- 配置系统可以适配不同的配置格式
- 错误处理系统可以扩展新的错误类型

## 依赖

- `serde` - 序列化支持
- `thiserror` - 错误处理
- `anyhow` - 错误链
- `std::process` - 命令执行

## 注意事项

- 需要系统安装 opkg 命令
- 某些操作需要适当的权限
- 配置文件路径可能因系统而异
- 使用 `#[cfg(opkg)]` 条件编译以支持可选的 opkg 功能
