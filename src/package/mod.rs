pub mod config;
pub mod error;
pub mod examples;
#[cfg(opkg)]
pub mod opkg;
pub use config::{ArchType, CompressType, Config, DestType, OptionType, SetConfig, SrcType};

use error::Result;
use serde::{Deserialize, Serialize};

#[cfg(opkg)]
pub use opkg::{Opkg, OpkgPackage};

use crate::package;

pub trait PackageManager {
    fn update_index(&self) -> Result<Vec<PackageInfo>>;
    fn list_packages(&self) -> Result<Vec<PackageInfo>>;
    fn install_package(&self, packages: Vec<&str>, dest: Option<&str>) -> Result<()>;
    fn remove_package(&self, packages: Vec<&str>) -> Result<()>;
    fn list_upgradable(&self) -> Result<Vec<PackageInfo>>;
    fn get_config(&self, path: Option<String>) -> Result<Config>;
    fn set_config(&self, config: Config, path: Option<String>) -> Result<()>;
    fn info_package(&self, p: &str) -> Result<PackageInfo>;
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageInfo {
    /// Application package name
    pub name: String,
    /// Application version
    pub version: String,
    /// Application description
    pub description: Option<String>,
    /// Application architecture
    pub architecture: Option<String>,
    /// Whether the application is installed
    pub installed: bool,

    /// Dependencies
    pub dependencies: Vec<String>,
}

/// Factory function to create the appropriate package manager based on the system
#[cfg(opkg)]
pub fn create_package_manager(
) -> Box<dyn PackageManager> {
    Box::new(Opkg::new())
}

/// Create an opkg package manager with custom configuration
#[cfg(opkg)]
pub fn create_opkg_manager(config_file: Option<String>, dest: Option<String>) -> Opkg {
    let mut opkg = Opkg::new();
    if let Some(config) = config_file {
        opkg = opkg.with_config_file(config);
    }
    if let Some(dest) = dest {
        opkg = opkg.with_dest(dest);
    }
    opkg
}

/// Convenience function to list all available packages
#[cfg(opkg)]
pub fn list_available_packages() -> Result<Vec<OpkgPackage>> {
    let opkg = Opkg::new();
    opkg.list_packages()
}

/// Convenience function to list all installed packages
#[cfg(opkg)]
pub fn list_installed_packages() -> Result<Vec<OpkgPackage>> {
    let opkg = Opkg::new();
    opkg.list_installed()
}

/// Convenience function to install packages
#[cfg(opkg)]
pub fn install_packages(packages: Vec<&str>, dest: Option<&str>) -> Result<()> {
    let opkg = Opkg::new();
    opkg.install_package(packages, dest)
}

/// Convenience function to remove packages
#[cfg(opkg)]
pub fn remove_packages(packages: Vec<&str>) -> Result<()> {
    let opkg = Opkg::new();
    opkg.remove_package(packages)
}

/// Convenience function to update package index
#[cfg(opkg)]
pub fn update_package_index() -> Result<Vec<OpkgPackage>> {
    let opkg = Opkg::new();
    opkg.update_index()
}
