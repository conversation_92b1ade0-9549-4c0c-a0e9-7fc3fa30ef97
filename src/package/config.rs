use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use std::fs;
use std::io::{<PERSON><PERSON><PERSON><PERSON>, BufReader};
use std::path::Path;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub enum CompressType {
    None,
    Gz,
}

impl std::fmt::Display for CompressType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CompressType::None => write!(f, ""),
            CompressType::Gz => write!(f, "/gz"),
        }
    }
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SrcType {
    pub name: String,
    pub compress_type: CompressType,
    pub url: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ArchType {
    pub arch: String,
    pub priority: u32,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DestType {
    pub name: String,
    pub path: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct OptionType {
    pub key: String,
    pub value: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub destinations: Vec<DestType>,
    pub options: Vec<OptionType>,
    pub architectures: Vec<ArchType>,
    pub sources: Vec<SrcType>,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            destinations: Vec::new(),
            options: Vec::new(),
            architectures: Vec::new(),
            sources: Vec::new(),
        }
    }
}

impl Config {
    pub fn new() -> Self {
        Self::default()
    }

    /// Parse configuration from a file
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let file = fs::File::open(path)?;
        let reader = BufReader::new(file);
        Self::from_reader(reader)
    }

    /// Parse configuration from a reader
    pub fn from_reader<R: BufRead>(reader: R) -> Result<Self> {
        let mut config = Config::new();

        for line in reader.lines() {
            let line = line?;
            let line = line.trim();

            // Skip empty lines and comments
            if line.is_empty() || line.starts_with('#') {
                continue;
            }

            config.parse_line(line)?;
        }

        Ok(config)
    }

    /// Parse a single configuration line
    fn parse_line(&mut self, line: &str) -> Result<()> {
        let parts: Vec<&str> = line.split_whitespace().collect();

        if parts.is_empty() {
            return Ok(());
        }

        match parts[0] {
            "dest" => {
                if parts.len() != 3 {
                    return Err(anyhow!("Invalid dest line: {}", line));
                }
                self.destinations.push(DestType {
                    name: parts[1].to_string(),
                    path: parts[2].to_string(),
                });
            }
            "option" => {
                if parts.len() != 3 {
                    return Err(anyhow!("Invalid option line: {}", line));
                }
                self.options.push(OptionType {
                    key: parts[1].to_string(),
                    value: parts[2].to_string(),
                });
            }
            "arch" => {
                if parts.len() != 3 {
                    return Err(anyhow!("Invalid arch line: {}", line));
                }
                let priority = parts[2]
                    .parse::<u32>()
                    .map_err(|_| anyhow!("Invalid priority in arch line: {}", line))?;
                self.architectures.push(ArchType {
                    arch: parts[1].to_string(),
                    priority,
                });
            }
            "src" | "src/gz" => {
                if parts.len() != 3 {
                    return Err(anyhow!("Invalid src line: {}", line));
                }
                let compress_type = if parts[0] == "src/gz" {
                    CompressType::Gz
                } else {
                    CompressType::None
                };
                self.sources.push(SrcType {
                    name: parts[1].to_string(),
                    compress_type,
                    url: parts[2].to_string(),
                });
            }
            _ => {
                return Err(anyhow!("Unknown configuration directive: {}", parts[0]));
            }
        }

        Ok(())
    }

    /// Convert configuration back to string format
    pub fn to_string(&self) -> String {
        let mut lines = Vec::new();

        // Add destinations
        for dest in &self.destinations {
            lines.push(format!("dest {} {}", dest.name, dest.path));
        }

        // Add options
        for option in &self.options {
            lines.push(format!("option {} {}", option.key, option.value));
        }

        // Add architectures
        for arch in &self.architectures {
            lines.push(format!("arch {} {}", arch.arch, arch.priority));
        }

        // Add sources
        for src in &self.sources {
            lines.push(format!("src{} {} {}", src.compress_type, src.name, src.url));
        }

        lines.join("\n")
    }

    /// Write configuration to a file
    pub fn write_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        fs::write(path, self.to_string())?;
        Ok(())
    }
}

pub trait SetConfig {
    /// Update destination configuration
    fn update_dest(&mut self, name: &str, path: &str) -> Result<()>;

    /// Update source configuration
    fn update_src(&mut self, name: &str, compress_type: CompressType, url: &str) -> Result<()>;

    /// Update architecture configuration
    fn update_arch(&mut self, arch: &str, priority: u32) -> Result<()>;

    /// Update option configuration
    fn update_option(&mut self, key: &str, value: &str) -> Result<()>;

    /// Remove destination by name
    fn remove_dest(&mut self, name: &str) -> Result<()>;

    /// Remove source by name
    fn remove_src(&mut self, name: &str) -> Result<()>;

    /// Remove architecture by name
    fn remove_arch(&mut self, arch: &str) -> Result<()>;

    /// Remove option by key
    fn remove_option(&mut self, key: &str) -> Result<()>;
}

impl SetConfig for Config {
    fn update_dest(&mut self, name: &str, path: &str) -> Result<()> {
        // Check if destination already exists and update it
        for dest in &mut self.destinations {
            if dest.name == name {
                dest.path = path.to_string();
                return Ok(());
            }
        }

        // If not found, add new destination
        self.destinations.push(DestType {
            name: name.to_string(),
            path: path.to_string(),
        });

        Ok(())
    }

    fn update_src(&mut self, name: &str, compress_type: CompressType, url: &str) -> Result<()> {
        // Check if source already exists and update it
        for src in &mut self.sources {
            if src.name == name {
                src.compress_type = compress_type;
                src.url = url.to_string();
                return Ok(());
            }
        }

        // If not found, add new source
        self.sources.push(SrcType {
            name: name.to_string(),
            compress_type,
            url: url.to_string(),
        });

        Ok(())
    }

    fn update_arch(&mut self, arch: &str, priority: u32) -> Result<()> {
        // Check if architecture already exists and update it
        for arch_entry in &mut self.architectures {
            if arch_entry.arch == arch {
                arch_entry.priority = priority;
                return Ok(());
            }
        }

        // If not found, add new architecture
        self.architectures.push(ArchType {
            arch: arch.to_string(),
            priority,
        });

        Ok(())
    }

    fn update_option(&mut self, key: &str, value: &str) -> Result<()> {
        // Check if option already exists and update it
        for option in &mut self.options {
            if option.key == key {
                option.value = value.to_string();
                return Ok(());
            }
        }

        // If not found, add new option
        self.options.push(OptionType {
            key: key.to_string(),
            value: value.to_string(),
        });

        Ok(())
    }

    fn remove_dest(&mut self, name: &str) -> Result<()> {
        let initial_len = self.destinations.len();
        self.destinations.retain(|dest| dest.name != name);

        if self.destinations.len() == initial_len {
            return Err(anyhow!("Destination '{}' not found", name));
        }

        Ok(())
    }

    fn remove_src(&mut self, name: &str) -> Result<()> {
        let initial_len = self.sources.len();
        self.sources.retain(|src| src.name != name);

        if self.sources.len() == initial_len {
            return Err(anyhow!("Source '{}' not found", name));
        }

        Ok(())
    }

    fn remove_arch(&mut self, arch: &str) -> Result<()> {
        let initial_len = self.architectures.len();
        self.architectures
            .retain(|arch_entry| arch_entry.arch != arch);

        if self.architectures.len() == initial_len {
            return Err(anyhow!("Architecture '{}' not found", arch));
        }

        Ok(())
    }

    fn remove_option(&mut self, key: &str) -> Result<()> {
        let initial_len = self.options.len();
        self.options.retain(|option| option.key != key);

        if self.options.len() == initial_len {
            return Err(anyhow!("Option '{}' not found", key));
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Cursor;

    #[test]
    fn test_parse_config_from_example() {
        let config_text = r#"dest root /
dest ram /tmp
option lists_dir /var/opkg-lists
option overlay_root /overlay

arch all 100
arch arm_cortex-a7 200
arch arm_cortex-a7_vfpv4 300
arch arm_cortex-a7_neon-vfpv4 400

#src/gz share *************/share/
#src/gz base https://archive.openwrt.org/releases/23.05.4/packages/arm_cortex-a7/base/
src/gz local file:///root/ipkgs"#;

        let cursor = Cursor::new(config_text);
        let config = Config::from_reader(cursor).unwrap();

        // Test destinations
        assert_eq!(config.destinations.len(), 2);
        assert_eq!(config.destinations[0].name, "root");
        assert_eq!(config.destinations[0].path, "/");
        assert_eq!(config.destinations[1].name, "ram");
        assert_eq!(config.destinations[1].path, "/tmp");

        // Test options
        assert_eq!(config.options.len(), 2);
        assert_eq!(config.options[0].key, "lists_dir");
        assert_eq!(config.options[0].value, "/var/opkg-lists");
        assert_eq!(config.options[1].key, "overlay_root");
        assert_eq!(config.options[1].value, "/overlay");

        // Test architectures
        assert_eq!(config.architectures.len(), 4);
        assert_eq!(config.architectures[0].arch, "all");
        assert_eq!(config.architectures[0].priority, 100);
        assert_eq!(config.architectures[3].arch, "arm_cortex-a7_neon-vfpv4");
        assert_eq!(config.architectures[3].priority, 400);

        // Test sources
        assert_eq!(config.sources.len(), 1);
        assert_eq!(config.sources[0].name, "local");
        assert_eq!(config.sources[0].compress_type, CompressType::Gz);
        assert_eq!(config.sources[0].url, "file:///root/ipkgs");
    }

    #[test]
    fn test_config_to_string() {
        let mut config = Config::new();
        config.destinations.push(DestType {
            name: "root".to_string(),
            path: "/".to_string(),
        });
        config.options.push(OptionType {
            key: "lists_dir".to_string(),
            value: "/var/opkg-lists".to_string(),
        });
        config.architectures.push(ArchType {
            arch: "all".to_string(),
            priority: 100,
        });
        config.sources.push(SrcType {
            name: "local".to_string(),
            compress_type: CompressType::Gz,
            url: "file:///root/ipkgs".to_string(),
        });

        let config_string = config.to_string();
        assert!(config_string.contains("dest root /"));
        assert!(config_string.contains("option lists_dir /var/opkg-lists"));
        assert!(config_string.contains("arch all 100"));
        assert!(config_string.contains("src/gz local file:///root/ipkgs"));
    }

    #[test]
    fn test_set_config_trait() {
        let mut config = Config::new();

        // Test updating destinations
        config.update_dest("root", "/").unwrap();
        assert_eq!(config.destinations.len(), 1);
        assert_eq!(config.destinations[0].name, "root");
        assert_eq!(config.destinations[0].path, "/");

        // Test updating existing destination
        config.update_dest("root", "/new/path").unwrap();
        assert_eq!(config.destinations.len(), 1);
        assert_eq!(config.destinations[0].path, "/new/path");

        // Test updating sources
        config
            .update_src("local", CompressType::Gz, "file:///root/ipkgs")
            .unwrap();
        assert_eq!(config.sources.len(), 1);
        assert_eq!(config.sources[0].name, "local");

        // Test removing items
        config.remove_dest("root").unwrap();
        assert_eq!(config.destinations.len(), 0);

        // Test error when removing non-existent item
        assert!(config.remove_dest("nonexistent").is_err());
    }
}
