use super::{error::Package<PERSON><PERSON>r, Config, PackageManager, Result, PackageInfo};
use serde::{Deserialize, Serialize};
use std::process::{Command, Stdio};
use std::str;

const CONFIG_PATH: &'static str = "/etc/opkg/opkg.conf";

#[derive(Debug, Clone)]
pub struct Opkg {
    pub config_file: Option<String>,
    pub verbosity: u8,
    pub dest: Option<String>,
    pub offline_root: Option<String>,
    pub cache_dir: Option<String>,
    pub tmp_dir: Option<String>,
    pub lists_dir: Option<String>,
}


impl Default for Opkg {
    fn default() -> Self {
        Self::new()
    }
}

impl Opkg {
    pub fn new() -> Self {
        Self {
            config_file: None,
            verbosity: 1,
            dest: None,
            offline_root: None,
            cache_dir: None,
            tmp_dir: None,
            lists_dir: None,
        }
    }

    pub fn with_config_file(mut self, config_file: String) -> Self {
        self.config_file = Some(config_file);
        self
    }

    pub fn with_verbosity(mut self, verbosity: u8) -> Self {
        self.verbosity = verbosity;
        self
    }

    pub fn with_dest(mut self, dest: String) -> Self {
        self.dest = Some(dest);
        self
    }

    fn build_command(&self, subcommand: &str) -> Command {
        let mut cmd = Command::new("opkg");

        // Add global options
        if let Some(ref config_file) = self.config_file {
            cmd.arg("-f").arg(config_file);
        }

        if self.verbosity != 1 {
            cmd.arg(format!("-V{}", self.verbosity));
        }

        if let Some(ref dest) = self.dest {
            cmd.arg("-d").arg(dest);
        }

        if let Some(ref offline_root) = self.offline_root {
            cmd.arg("-o").arg(offline_root);
        }

        if let Some(ref cache_dir) = self.cache_dir {
            cmd.arg("--cache-dir").arg(cache_dir);
        }

        if let Some(ref tmp_dir) = self.tmp_dir {
            cmd.arg("-t").arg(tmp_dir);
        }

        if let Some(ref lists_dir) = self.lists_dir {
            cmd.arg("-l").arg(lists_dir);
        }

        cmd.arg(subcommand);
        cmd
    }

    fn execute_command(&self, mut cmd: Command) -> Result<String> {
        let output = cmd
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output()
            .map_err(|e| PackageError::CommandError(format!("Failed to execute opkg: {}", e)))?;

        if !output.status.success() {
            let stderr = str::from_utf8(&output.stderr)
                .unwrap_or("Unknown error")
                .trim();
            return Err(PackageError::CommandError(format!(
                "opkg failed: {}",
                stderr
            )));
        }

        let stdout = str::from_utf8(&output.stdout)
            .map_err(|e| PackageError::ParseError(format!("Invalid UTF-8 output: {}", e)))?;

        Ok(stdout.to_string())
    }

    fn parse_package_list(&self, output: &str, installed: bool) -> Vec<PackageInfo> {
        let mut packages = Vec::new();
        let package_blocks: Vec<&str> = output.split("\n\n").collect();

        for block in package_blocks {
            let block = block.trim();
            if block.is_empty() {
                continue;
            }

            let mut name = None;
            let mut version = None;
            let mut description = None;
            let mut architecture = None;
            let mut status_installed = installed;

            for line in block.lines() {
                let line = line.trim();
                if line.is_empty() {
                    continue;
                }

                if let Some((key, value)) = line.split_once(':') {
                    let key = key.trim();
                    let value = value.trim();

                    match key {
                        "Package" => name = Some(value.to_string()),
                        "Version" => version = Some(value.to_string()),
                        "Description" => description = Some(value.to_string()),
                        "Architecture" => architecture = Some(value.to_string()),
                        "Status" => {
                            // Parse status to determine if installed
                            // Status format: "install user installed" or "unknown ok not-installed"
                            status_installed = value.contains("installed") && !value.contains("not-installed");
                        },
                        _ => {} // Ignore other fields
                    }
                }
            }

            if let Some(pkg_name) = name {
                packages.push(PackageInfo {
                    name: pkg_name,
                    version,
                    description,
                    architecture,
                    installed: status_installed,
                });
            }
        }

        packages
    }
    
}

impl PackageManager for Opkg {
    fn update_index(&self) -> Result<Self::PackageList> {
        let cmd = self.build_command("update");
        self.execute_command(cmd)?;

        // After update, return the list of available packages
        self.list_packages()
    }

    fn list_packages(&self) -> Result<Self::PackageList> {
        let cmd = self.build_command("list");
        let output = self.execute_command(cmd)?;

        Ok(self.parse_package_list(&output, false))
    }

    fn install_package(&self, packages: Vec<&str>, dest: Option<&str>) -> Result<()> {
        let mut cmd = self.build_command("install");

        if let Some(dest) = dest {
            cmd.arg("-d").arg(dest);
        }

        for package in packages {
            cmd.arg(package);
        }

        self.execute_command(cmd)?;
        Ok(())
    }

    fn remove_package(&self, packages: Vec<&str>) -> Result<()> {
        let mut cmd = self.build_command("remove");

        for package in packages {
            cmd.arg(package);
        }

        self.execute_command(cmd)?;
        Ok(())
    }

    fn list_upgradable(&self) -> Result<Self::PackageList> {
        let cmd = self.build_command("list-upgradable");
        let output = self.execute_command(cmd)?;
        Ok(self.parse_package_list(&output, true))
    }

    fn get_config(&self, path: Option<String>) -> Result<Self::ConfigType> {
        let config_path = path.unwrap_or_else(|| CONFIG_PATH.to_string());
        Config::from_file(config_path).map_err(|e| PackageError::GetConfigError(e.to_string()))
    }

    fn set_config(&self, config: Self::ConfigType, path: Option<String>) -> Result<()> {
        let config_path = path.unwrap_or_else(|| CONFIG_PATH.to_string());
        config
            .write_to_file(config_path)
            .map_err(|e| PackageError::SetConfigError(e.to_string()))
    }
}

#[cfg(opkg_extended)]
impl Opkg {
    fn list_installed(&self) -> Result<Vec<OpkgPackage>> {
        let cmd = self.build_command("list-installed");
        let output = self.execute_command(cmd)?;
        Ok(self.parse_package_list(&output, true))
    }

    fn search_package(&self, query: &str) -> Result<Vec<OpkgPackage>> {
        let mut cmd = self.build_command("search");
        cmd.arg(query);
        let output = self.execute_command(cmd)?;
        Ok(self.parse_package_list(&output, false))
    }

    fn find_package(&self, regexp: &str) -> Result<Vec<OpkgPackage>> {
        let mut cmd = self.build_command("find");
        cmd.arg(regexp);
        let output = self.execute_command(cmd)?;
        Ok(self.parse_package_list(&output, false))
    }

    fn info_package(&self, package: &str) -> Result<String> {
        let mut cmd = self.build_command("info");
        cmd.arg(package);
        self.execute_command(cmd)
    }

    fn status_package(&self, package: &str) -> Result<String> {
        let mut cmd = self.build_command("status");
        cmd.arg(package);
        self.execute_command(cmd)
    }

    fn files_package(&self, package: &str) -> Result<Vec<String>> {
        let mut cmd = self.build_command("files");
        cmd.arg(package);
        let output = self.execute_command(cmd)?;

        Ok(output
            .lines()
            .map(|line| line.trim().to_string())
            .filter(|line| !line.is_empty())
            .collect())
    }

    fn download_package(&self, package: &str, dest_dir: Option<&str>) -> Result<()> {
        let mut cmd = self.build_command("download");
        cmd.arg(package);

        if let Some(dir) = dest_dir {
            cmd.current_dir(dir);
        }

        self.execute_command(cmd)?;
        Ok(())
    }

    fn configure_package(&self, packages: Vec<&str>) -> Result<()> {
        let mut cmd = self.build_command("configure");

        for package in packages {
            cmd.arg(package);
        }

        self.execute_command(cmd)?;
        Ok(())
    }

    fn upgrade_packages(&self, packages: Option<Vec<&str>>) -> Result<()> {
        let mut cmd = self.build_command("upgrade");

        if let Some(packages) = packages {
            for package in packages {
                cmd.arg(package);
            }
        }

        self.execute_command(cmd)?;
        Ok(())
    }

    fn clean_cache(&self) -> Result<()> {
        let cmd = self.build_command("clean");
        self.execute_command(cmd)?;
        Ok(())
    }
}
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_opkg_creation() {
        let opkg = Opkg::new();
        assert_eq!(opkg.verbosity, 1);
        assert!(opkg.config_file.is_none());
        assert!(opkg.dest.is_none());
    }

    #[test]
    fn test_opkg_builder_pattern() {
        let opkg = Opkg::new()
            .with_config_file("/custom/opkg.conf".to_string())
            .with_verbosity(3)
            .with_dest("root".to_string());

        assert_eq!(opkg.config_file, Some("/custom/opkg.conf".to_string()));
        assert_eq!(opkg.verbosity, 3);
        assert_eq!(opkg.dest, Some("root".to_string()));
    }

    #[test]
    fn test_parse_package_list() {
        let opkg = Opkg::new();
        let output = r#"Package: test
Version: 0.1
Status: unknown ok not-installed
Section: Applications
Architecture: all
MD5Sum: 7e5acb77b5442fdff33b66e2e0010c46
Size: 786
Filename: test-0.1.ipk
Description: default ipkg

Package: traceroute-2.1.ipk
Version: 2.1
Status: install user installed
Architecture: all
Installed-Size: 82532
Installed-Time: 1745829168

Package: example
Version: 0.0
Depends: test
Status: unknown ok not-installed
Section: Applications
Architecture: all
MD5Sum: 00a77ef73645295047928581264b06db
Size: 903
Filename: example.ipk"#;

        let packages = opkg.parse_package_list(output, false);

        assert_eq!(packages.len(), 3);

        // Test first package (not installed)
        assert_eq!(packages[0].name, "test");
        assert_eq!(packages[0].version, Some("0.1".to_string()));
        assert_eq!(packages[0].description, Some("default ipkg".to_string()));
        assert_eq!(packages[0].architecture, Some("all".to_string()));
        assert!(!packages[0].installed);

        // Test second package (installed)
        assert_eq!(packages[1].name, "traceroute-2.1.ipk");
        assert_eq!(packages[1].version, Some("2.1".to_string()));
        assert_eq!(packages[1].architecture, Some("all".to_string()));
        assert!(packages[1].installed);

        // Test third package (not installed, no description)
        assert_eq!(packages[2].name, "example");
        assert_eq!(packages[2].version, Some("0.0".to_string()));
        assert!(!packages[2].installed);
    }

    #[test]
    fn test_build_command() {
        let opkg = Opkg::new()
            .with_config_file("/etc/opkg/custom.conf".to_string())
            .with_verbosity(2)
            .with_dest("root".to_string());

        let cmd = opkg.build_command("list");
        let args: Vec<String> = cmd
            .get_args()
            .map(|s| s.to_string_lossy().to_string())
            .collect();

        assert!(args.contains(&"-f".to_string()));
        assert!(args.contains(&"/etc/opkg/custom.conf".to_string()));
        assert!(args.contains(&"-V2".to_string()));
        assert!(args.contains(&"-d".to_string()));
        assert!(args.contains(&"root".to_string()));
        assert!(args.contains(&"list".to_string()));
    }
}
