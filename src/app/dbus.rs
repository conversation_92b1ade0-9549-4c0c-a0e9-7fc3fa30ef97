use crate::app::{A<PERSON><PERSON><PERSON><PERSON>, AppInfo, DaemonState, AppError, AppR<PERSON>ult};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use zbus::{interface, Connection, Result as ZbusResult};
use zvariant::{Value, OwnedValue};
use log::{info, error, debug};

/// D-Bus interface for application management
pub struct AppManagerDBus {
    manager: Arc<RwLock<AppManager>>,
}

impl AppManagerDBus {
    /// Create a new D-Bus interface wrapper
    pub fn new(manager: Arc<RwLock<AppManager>>) -> Self {
        Self { manager }
    }

    /// Start the D-Bus service
    pub async fn start_service(manager: Arc<RwLock<AppManager>>) -> AppResult<()> {
        info!("Starting D-Bus service for AppManager");

        let app_manager_dbus = AppManagerDBus::new(manager.clone());

        let connection = Connection::system().await?
            .name("mgc.platform.AppManager1")?
            .serve_at("/mgc/platform/AppManager1", app_manager_dbus)?
            .build()
            .await?;

        info!("D-Bus service started successfully");

        // Keep the connection alive
        loop {
            connection.executor().tick().await;
        }
    }

    /// Convert AppInfo to D-Bus state tuple
    fn app_info_to_dbus_state(app_info: &AppInfo) -> (u8, u8, u8) {
        app_info.to_dbus_state()
    }

    /// Convert app list to D-Bus format
    fn apps_to_dbus_format(apps: &[AppInfo]) -> Vec<String> {
        apps.iter()
            .map(|app| format!("{}:{}", app.name, app.version))
            .collect()
    }
}

#[interface(name = "mgc.platform.AppManager1")]
impl AppManagerDBus {
    /// Run method - Set a group of applications' start/stop state
    async fn run(&self, apps: Vec<(OwnedValue, bool)>) -> ZbusResult<()> {
        debug!("D-Bus Run called with {} applications", apps.len());

        let manager = self.manager.clone();
        let mut mgr = manager.write().await;

        for (app_id, should_run) in apps {
            let app_name = match app_id.try_into() {
                Ok(Value::Str(name)) => name.to_string(),
                _ => {
                    error!("Invalid application ID format");
                    continue;
                }
            };

            let result = if should_run {
                mgr.start_application(&app_name, false).await
            } else {
                mgr.stop_application(&app_name, false).await
            };

            if let Err(e) = result {
                error!("Failed to {} application {}: {}", 
                    if should_run { "start" } else { "stop" }, app_name, e);
            }
        }

        Ok(())
    }

    /// SetAutoStart method - Set a group of applications' auto-start state
    async fn set_auto_start(&self, apps: Vec<(OwnedValue, bool)>) -> ZbusResult<()> {
        debug!("D-Bus SetAutoStart called with {} applications", apps.len());

        let manager = self.manager.clone();
        let mut mgr = manager.write().await;

        for (app_id, enabled) in apps {
            let app_name = match app_id.try_into() {
                Ok(Value::Str(name)) => name.to_string(),
                _ => {
                    error!("Invalid application ID format");
                    continue;
                }
            };

            let result = if enabled {
                mgr.enable_auto_start(&app_name).await
            } else {
                mgr.disable_auto_start(&app_name).await
            };

            if let Err(e) = result {
                error!("Failed to set auto-start for {}: {}", app_name, e);
            }
        }

        Ok(())
    }

    /// Daemonize method - Set a group of applications' daemon state
    async fn daemonize(&self, apps: Vec<(OwnedValue, bool)>) -> ZbusResult<()> {
        debug!("D-Bus Daemonize called with {} applications", apps.len());

        let manager = self.manager.clone();
        let mut mgr = manager.write().await;

        for (app_id, enabled) in apps {
            let app_name = match app_id.try_into() {
                Ok(Value::Str(name)) => name.to_string(),
                _ => {
                    error!("Invalid application ID format");
                    continue;
                }
            };

            let daemon_state = if enabled {
                DaemonState::Enabled
            } else {
                DaemonState::Disabled
            };

            if let Err(e) = mgr.set_daemon_state(&app_name, daemon_state).await {
                error!("Failed to set daemon state for {}: {}", app_name, e);
            }
        }

        Ok(())
    }

    /// State method - Get state information for applications
    async fn state(&self, app_ids: Vec<OwnedValue>) -> ZbusResult<Vec<String>> {
        debug!("D-Bus State called for {} applications", app_ids.len());

        let manager = self.manager.clone();
        let mgr = manager.read().await;
        let mut states = Vec::new();

        for app_id in app_ids {
            let app_name = match app_id.try_into() {
                Ok(Value::Str(name)) => name.to_string(),
                _ => {
                    error!("Invalid application ID format");
                    states.push("error:invalid_id".to_string());
                    continue;
                }
            };

            match mgr.get_application_state(&app_name).await {
                Ok(app_info) => {
                    let state_json = serde_json::json!({
                        "name": app_info.name,
                        "state": app_info.state.to_string(),
                        "auto_start": app_info.auto_start.to_string(),
                        "daemon": app_info.daemon_state.to_string(),
                        "pid": app_info.resource_usage.pid,
                        "cpu_percent": app_info.resource_usage.cpu_percent,
                        "memory_bytes": app_info.resource_usage.memory_bytes,
                        "uptime_seconds": app_info.resource_usage.uptime_seconds
                    });
                    states.push(state_json.to_string());
                }
                Err(e) => {
                    error!("Failed to get state for {}: {}", app_name, e);
                    states.push(format!("error:{}", e));
                }
            }
        }

        Ok(states)
    }

    /// Ver method - Get version information for applications
    async fn ver(&self, app_ids: Vec<OwnedValue>) -> ZbusResult<Vec<String>> {
        debug!("D-Bus Ver called for {} applications", app_ids.len());

        let manager = self.manager.clone();
        let mgr = manager.read().await;
        let mut versions = Vec::new();

        for app_id in app_ids {
            let app_name = match app_id.try_into() {
                Ok(Value::Str(name)) => name.to_string(),
                _ => {
                    error!("Invalid application ID format");
                    versions.push("error:invalid_id".to_string());
                    continue;
                }
            };

            match mgr.get_application_version(&app_name).await {
                Ok(version) => versions.push(version),
                Err(e) => {
                    error!("Failed to get version for {}: {}", app_name, e);
                    versions.push(format!("error:{}", e));
                }
            }
        }

        Ok(versions)
    }

    /// Install method - Install packages
    async fn install(&self, install_root: String, packages: Vec<String>) -> ZbusResult<()> {
        debug!("D-Bus Install called for packages: {:?}", packages);

        let manager = self.manager.clone();
        let mut mgr = manager.write().await;

        let dest = if install_root.is_empty() {
            None
        } else {
            Some(std::path::PathBuf::from(install_root))
        };

        if let Err(e) = mgr.install_packages(packages.clone(), dest).await {
            error!("Failed to install packages {:?}: {}", packages, e);
            return Err(zbus::Error::Failure(format!("Installation failed: {}", e)));
        }

        Ok(())
    }

    /// Remove method - Remove packages
    async fn remove(&self, packages: Vec<String>) -> ZbusResult<()> {
        debug!("D-Bus Remove called for packages: {:?}", packages);

        let manager = self.manager.clone();
        let mut mgr = manager.write().await;

        if let Err(e) = mgr.remove_packages(packages.clone()).await {
            error!("Failed to remove packages {:?}: {}", packages, e);
            return Err(zbus::Error::Failure(format!("Removal failed: {}", e)));
        }

        Ok(())
    }

    /// Update method - Update package index
    async fn update(&self) -> ZbusResult<Vec<String>> {
        debug!("D-Bus Update called");

        let manager = self.manager.clone();
        let mgr = manager.read().await;

        match mgr.update_package_index().await {
            Ok(packages) => Ok(packages),
            Err(e) => {
                error!("Failed to update package index: {}", e);
                Err(zbus::Error::Failure(format!("Update failed: {}", e)))
            }
        }
    }

    /// Upgrade method - Upgrade packages
    async fn upgrade(&self, packages: Vec<String>) -> ZbusResult<()> {
        debug!("D-Bus Upgrade called for packages: {:?}", packages);

        let manager = self.manager.clone();
        let mut mgr = manager.write().await;

        if let Err(e) = mgr.upgrade_packages(packages.clone()).await {
            error!("Failed to upgrade packages {:?}: {}", packages, e);
            return Err(zbus::Error::Failure(format!("Upgrade failed: {}", e)));
        }

        Ok(())
    }

    /// ListInstalled method - List installed packages
    async fn list_installed(&self) -> ZbusResult<Vec<String>> {
        debug!("D-Bus ListInstalled called");

        let manager = self.manager.clone();
        let mgr = manager.read().await;

        match mgr.list_installed_packages().await {
            Ok(packages) => Ok(packages),
            Err(e) => {
                error!("Failed to list installed packages: {}", e);
                Err(zbus::Error::Failure(format!("List failed: {}", e)))
            }
        }
    }

    /// ListUpgradable method - List upgradable packages
    async fn list_upgradable(&self) -> ZbusResult<Vec<String>> {
        debug!("D-Bus ListUpgradable called");

        let manager = self.manager.clone();
        let mgr = manager.read().await;

        match mgr.list_upgradable_packages().await {
            Ok(packages) => Ok(packages),
            Err(e) => {
                error!("Failed to list upgradable packages: {}", e);
                Err(zbus::Error::Failure(format!("List failed: {}", e)))
            }
        }
    }

    /// AddPackageSource method - Add package source
    async fn add_package_source(&self, _sources: Vec<String>) -> ZbusResult<()> {
        // TODO: Implement package source management
        debug!("D-Bus AddPackageSource called (not implemented)");
        Err(zbus::Error::Failure("Not implemented".to_string()))
    }

    /// DelPackageSource method - Delete package source
    async fn del_package_source(&self, _sources: Vec<String>) -> ZbusResult<()> {
        // TODO: Implement package source management
        debug!("D-Bus DelPackageSource called (not implemented)");
        Err(zbus::Error::Failure("Not implemented".to_string()))
    }
}

impl From<AppError> for zbus::Error {
    fn from(err: AppError) -> Self {
        zbus::Error::Failure(err.to_string())
    }
}

impl From<std::io::Error> for zbus::Error {
    fn from(err: std::io::Error) -> Self {
        zbus::Error::Failure(err.to_string())
    }
}
