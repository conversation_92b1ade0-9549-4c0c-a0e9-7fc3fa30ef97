use clap::{Parser, Subcommand};
use std::path::PathBuf;

/// Application management command-line interface
#[derive(Parser, Debug)]
#[command(name = "app")]
#[command(about = "Application management tool")]
#[command(version = "1.0.0")]
pub struct AppCommand {
    /// Enable verbose output
    #[arg(short, long)]
    pub verbose: bool,

    /// Configuration file path
    #[arg(short, long)]
    pub config: Option<PathBuf>,

    /// Subcommand to execute
    #[command(subcommand)]
    pub command: AppSubCommand,
}

/// Available subcommands for application management
#[derive(Subcommand, Debug)]
pub enum AppSubCommand {
    /// List applications and their status
    List {
        /// Show only installed applications
        #[arg(long)]
        installed: bool,
        
        /// Show only running applications
        #[arg(long)]
        running: bool,
        
        /// Show detailed information
        #[arg(short, long)]
        detailed: bool,
    },

    /// Start an application
    Start {
        /// Application name
        name: String,
        
        /// Start in debug mode (no daemon monitoring)
        #[arg(short, long)]
        debug: bool,
        
        /// Additional arguments to pass to the application
        #[arg(long)]
        args: Option<String>,
    },

    /// Stop an application
    Stop {
        /// Application name
        name: String,
        
        /// Force stop (send SIGKILL)
        #[arg(short, long)]
        force: bool,
    },

    /// Enable auto-start for an application
    Enable {
        /// Application name or "all" for all applications
        name: String,
    },

    /// Disable auto-start for an application
    Disable {
        /// Application name or "all" for all applications
        name: String,
    },

    /// Get application version
    Ver {
        /// Application name
        name: String,
    },

    /// Get application state information
    State {
        /// Application name
        name: String,
        
        /// Output format (json, yaml, table)
        #[arg(short, long, default_value = "table")]
        format: String,
    },

    /// Manage daemon monitoring for applications
    Daemon {
        /// Application name
        name: String,
        
        /// Daemon command
        #[command(subcommand)]
        command: DaemonCommand,
    },

    /// Install packages
    Install {
        /// Installation destination
        #[arg(short, long)]
        dest: Option<PathBuf>,
        
        /// Package names to install
        packages: Vec<String>,
        
        /// Force installation (overwrite existing)
        #[arg(short, long)]
        force: bool,
    },

    /// Remove packages
    Remove {
        /// Package names to remove
        packages: Vec<String>,
        
        /// Remove dependencies as well
        #[arg(long)]
        auto_remove: bool,
    },

    /// Update package index
    Update,

    /// Upgrade packages
    Upgrade {
        /// Package names to upgrade (empty for all)
        packages: Vec<String>,
    },

    /// List upgradable packages
    ListUpgradable,

    /// Add package source
    AddSource {
        /// Source URL
        url: String,
        
        /// Source name
        name: Option<String>,
    },

    /// Remove package source
    RemoveSource {
        /// Source name or URL
        source: String,
    },

    /// Show package information
    Info {
        /// Package name
        package: String,
    },

    /// Search for packages
    Search {
        /// Search query
        query: String,
        
        /// Search in descriptions as well
        #[arg(long)]
        description: bool,
    },
}

/// Daemon management subcommands
#[derive(Subcommand, Debug)]
pub enum DaemonCommand {
    /// Start daemon monitoring
    Start,
    
    /// Stop daemon monitoring
    Stop,
    
    /// Suspend daemon monitoring
    Suspend,
    
    /// Resume daemon monitoring
    Resume,
    
    /// Set resource limits
    Limit {
        /// CPU limit percentage
        #[arg(long)]
        cpu: Option<u32>,
        
        /// Memory limit in MB
        #[arg(long)]
        memory: Option<u32>,
        
        /// Block I/O limit in MB/s
        #[arg(long)]
        blkio: Option<u32>,
    },
    
    /// Set resource thresholds
    Threshold {
        /// CPU threshold percentage
        #[arg(long)]
        cpu: Option<u32>,
        
        /// Memory threshold percentage
        #[arg(long)]
        memory: Option<u32>,
        
        /// Action to take when threshold is exceeded
        #[arg(long, default_value = "warn")]
        action: String,
    },
    
    /// Show daemon status
    Status,
}

impl AppCommand {
    /// Parse command line arguments
    pub fn parse_args() -> Self {
        Self::parse()
    }

    /// Parse from iterator (useful for testing)
    pub fn parse_from_iter<I, T>(iter: I) -> Self
    where
        I: IntoIterator<Item = T>,
        T: Into<std::ffi::OsString> + Clone,
    {
        Self::parse_from(iter)
    }
}

impl AppSubCommand {
    /// Get the command name as a string
    pub fn name(&self) -> &'static str {
        match self {
            AppSubCommand::List { .. } => "list",
            AppSubCommand::Start { .. } => "start",
            AppSubCommand::Stop { .. } => "stop",
            AppSubCommand::Enable { .. } => "enable",
            AppSubCommand::Disable { .. } => "disable",
            AppSubCommand::Ver { .. } => "ver",
            AppSubCommand::State { .. } => "state",
            AppSubCommand::Daemon { .. } => "daemon",
            AppSubCommand::Install { .. } => "install",
            AppSubCommand::Remove { .. } => "remove",
            AppSubCommand::Update => "update",
            AppSubCommand::Upgrade { .. } => "upgrade",
            AppSubCommand::ListUpgradable => "list-upgradable",
            AppSubCommand::AddSource { .. } => "add-source",
            AppSubCommand::RemoveSource { .. } => "remove-source",
            AppSubCommand::Info { .. } => "info",
            AppSubCommand::Search { .. } => "search",
        }
    }

    /// Check if this command requires root privileges
    pub fn requires_root(&self) -> bool {
        matches!(
            self,
            AppSubCommand::Start { .. }
                | AppSubCommand::Stop { .. }
                | AppSubCommand::Enable { .. }
                | AppSubCommand::Disable { .. }
                | AppSubCommand::Install { .. }
                | AppSubCommand::Remove { .. }
                | AppSubCommand::Update
                | AppSubCommand::Upgrade { .. }
                | AppSubCommand::AddSource { .. }
                | AppSubCommand::RemoveSource { .. }
                | AppSubCommand::Daemon { .. }
        )
    }

    /// Check if this command modifies system state
    pub fn is_mutating(&self) -> bool {
        !matches!(
            self,
            AppSubCommand::List { .. }
                | AppSubCommand::Ver { .. }
                | AppSubCommand::State { .. }
                | AppSubCommand::ListUpgradable
                | AppSubCommand::Info { .. }
                | AppSubCommand::Search { .. }
        )
    }
}

impl DaemonCommand {
    /// Get the daemon command name as a string
    pub fn name(&self) -> &'static str {
        match self {
            DaemonCommand::Start => "start",
            DaemonCommand::Stop => "stop",
            DaemonCommand::Suspend => "suspend",
            DaemonCommand::Resume => "resume",
            DaemonCommand::Limit { .. } => "limit",
            DaemonCommand::Threshold { .. } => "threshold",
            DaemonCommand::Status => "status",
        }
    }
}
