use crate::app::{AppScript, AppError, AppR<PERSON>ult};
use regex::Regex;
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::process::{Command, Stdio};
use log::{debug, warn, error};

/// Script parser for application configuration scripts
pub struct ScriptParser {
    /// Regex for parsing script stage numbers
    stage_regex: Regex,
    /// Regex for parsing variable assignments
    var_regex: Regex,
    /// Regex for parsing function definitions
    func_regex: Regex,
}

impl ScriptParser {
    /// Create a new script parser
    pub fn new() -> AppResult<Self> {
        let stage_regex = Regex::new(r"^S(\d+)(.*)$")
            .map_err(|e| AppError::ParseError(format!("Failed to compile stage regex: {}", e)))?;
        
        let var_regex = Regex::new(r#"^\s*([A-Z_]+)\s*=\s*["']?([^"']*)["']?\s*$"#)
            .map_err(|e| AppError::ParseError(format!("Failed to compile variable regex: {}", e)))?;
        
        let func_regex = Regex::new(r"^\s*(start|stop)\s*\(\s*\)\s*\{")
            .map_err(|e| AppError::ParseError(format!("Failed to compile function regex: {}", e)))?;

        Ok(Self {
            stage_regex,
            var_regex,
            func_regex,
        })
    }

    /// Parse an application script file
    pub fn parse_script<P: AsRef<Path>>(&self, script_path: P) -> AppResult<AppScript> {
        let path = script_path.as_ref();
        let filename = path.file_name()
            .and_then(|n| n.to_str())
            .ok_or_else(|| AppError::ParseError("Invalid script filename".to_string()))?;

        debug!("Parsing script: {}", path.display());

        // Parse stage number and name from filename
        let (stage, name) = self.parse_filename(filename)?;

        // Read and parse script content
        let content = fs::read_to_string(path)
            .map_err(|e| AppError::IoError(e))?;

        let (variables, has_custom_functions) = self.parse_content(&content)?;

        let script = AppScript {
            path: path.to_path_buf(),
            stage,
            executable: variables.get("EXECUTE").cloned(),
            args: variables.get("ARGS").cloned(),
            version_cmd: variables.get("VERSION_CMD").cloned(),
            has_custom_functions,
        };

        debug!("Parsed script: {:?}", script);
        Ok(script)
    }

    /// Parse filename to extract stage number and application name
    fn parse_filename(&self, filename: &str) -> AppResult<(Option<u32>, String)> {
        if let Some(captures) = self.stage_regex.captures(filename) {
            let stage_str = captures.get(1).unwrap().as_str();
            let stage = stage_str.parse::<u32>()
                .map_err(|e| AppError::ParseError(format!("Invalid stage number '{}': {}", stage_str, e)))?;
            
            let name = captures.get(2).unwrap().as_str().to_string();
            Ok((Some(stage), name))
        } else {
            // No stage number, just the application name
            Ok((None, filename.to_string()))
        }
    }

    /// Parse script content to extract variables and detect custom functions
    fn parse_content(&self, content: &str) -> AppResult<(HashMap<String, String>, bool)> {
        let mut variables = HashMap::new();
        let mut has_custom_functions = false;

        for line in content.lines() {
            let line = line.trim();
            
            // Skip comments and empty lines
            if line.is_empty() || line.starts_with('#') {
                continue;
            }

            // Check for variable assignments
            if let Some(captures) = self.var_regex.captures(line) {
                let var_name = captures.get(1).unwrap().as_str();
                let var_value = captures.get(2).unwrap().as_str();
                
                // Only capture known variables
                if matches!(var_name, "EXECUTE" | "ARGS" | "VERSION_CMD" | "DAEMON") {
                    variables.insert(var_name.to_string(), var_value.to_string());
                    debug!("Found variable: {}={}", var_name, var_value);
                }
            }

            // Check for custom function definitions
            if self.func_regex.is_match(line) {
                has_custom_functions = true;
                debug!("Found custom function in script");
            }
        }

        Ok((variables, has_custom_functions))
    }

    /// Find all script files in a directory
    pub fn find_scripts<P: AsRef<Path>>(&self, dir_path: P) -> AppResult<Vec<AppScript>> {
        let dir = dir_path.as_ref();
        
        if !dir.exists() {
            warn!("Script directory does not exist: {}", dir.display());
            return Ok(Vec::new());
        }

        let mut scripts = Vec::new();
        
        for entry in fs::read_dir(dir)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_file() {
                match self.parse_script(&path) {
                    Ok(script) => scripts.push(script),
                    Err(e) => {
                        warn!("Failed to parse script {}: {}", path.display(), e);
                    }
                }
            }
        }

        // Sort scripts by stage number for proper startup order
        scripts.sort_by_key(|s| s.stage.unwrap_or(99));
        
        debug!("Found {} scripts in {}", scripts.len(), dir.display());
        Ok(scripts)
    }

    /// Find a specific script by application name
    pub fn find_script_by_name<P: AsRef<Path>>(&self, dir_path: P, app_name: &str) -> AppResult<Option<AppScript>> {
        let scripts = self.find_scripts(dir_path)?;
        
        for script in scripts {
            let script_name = script.path.file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("");
            
            // Check exact match or stage-prefixed match
            if script_name == app_name {
                return Ok(Some(script));
            }
            
            if let Some(captures) = self.stage_regex.captures(script_name) {
                let name = captures.get(2).unwrap().as_str();
                if name == app_name {
                    return Ok(Some(script));
                }
            }
        }
        
        Ok(None)
    }
}

impl Default for ScriptParser {
    fn default() -> Self {
        Self::new().expect("Failed to create default script parser")
    }
}

/// Script executor for running application scripts
pub struct ScriptExecutor {
    /// Timeout for script execution
    timeout_secs: u64,
    stderr_log: bool,
}

impl ScriptExecutor {
    /// Create a new script executor
    pub fn new(timeout_secs: u64) -> Self {
        Self { timeout_secs }
    }

    /// Execute a script with the given action (start, stop, version)
    pub fn execute_script(&self, script: &AppScript, action: &str) -> AppResult<std::process::Output> {
        let script_path = &script.path;
        
        debug!("Executing script {} with action {}", script_path.display(), action);

        // Make sure script is executable
        self.ensure_executable(script_path)?;

        // Execute the script
        let output = Command::new(script_path)
            .arg(action)
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output()
            .map_err(|e| AppError::ScriptError(
                script_path.display().to_string(),
                format!("Failed to execute: {}", e)
            ))?;

        debug!("Script execution completed with status: {}", output.status);
        
        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(AppError::ScriptError(
                script_path.display().to_string(),
                format!("Script failed with status {}: {}", output.status, stderr)
            ));
        }

        Ok(output)
    }

    /// Ensure script file is executable
    fn ensure_executable(&self, script_path: &Path) -> AppResult<()> {
        use std::os::unix::fs::PermissionsExt;
        
        let metadata = fs::metadata(script_path)?;
        let permissions = metadata.permissions();
        let mode = permissions.mode();
        
        // Check if executable bit is set
        if mode & 0o111 == 0 {
            // Set executable permissions
            let new_mode = mode | 0o111;
            let mut new_permissions = permissions;
            new_permissions.set_mode(new_mode);
            fs::set_permissions(script_path, new_permissions)?;
            debug!("Set executable permissions for {}", script_path.display());
        }
        
        Ok(())
    }

    /// Execute a script and wait for completion with timeout
    pub fn execute_with_timeout(&self, script: &AppScript, action: &str) -> AppResult<std::process::Output> {
        // For now, use the simple execute_script method
        // TODO: Implement proper timeout handling using tokio::time::timeout
        self.execute_script(script, action)
    }

    /// Get the version of an application using its script
    pub fn get_version(&self, script: &AppScript, app_name: &str) -> AppResult<String> {
        let version_cmd = script.version_cmd.as_deref()
            .unwrap_or(&format!("{} -v", script.executable.as_deref().unwrap_or(app_name)));

        debug!("Getting version using command: {}", version_cmd);

        // Try script version action first
        if let Ok(output) = self.execute_script(script, "version") {
            let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
            if !version.is_empty() {
                return Ok(version);
            }
        }

        // Fall back to version command
        let parts: Vec<&str> = version_cmd.split_whitespace().collect();
        if parts.is_empty() {
            return Err(AppError::ParseError("Empty version command".to_string()));
        }

        let output = Command::new(parts[0])
            .args(&parts[1..])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output()
            .map_err(|e| AppError::ScriptError(
                app_name.to_string(),
                format!("Failed to get version: {}", e)
            ))?;

        if output.status.success() {
            let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
            Ok(version)
        } else {
            Err(AppError::ScriptError(
                app_name.to_string(),
                "Failed to get version".to_string()
            ))
        }
    }
}

impl Default for ScriptExecutor {
    fn default() -> Self {
        Self::new(30) // 30 second default timeout
    }
}
