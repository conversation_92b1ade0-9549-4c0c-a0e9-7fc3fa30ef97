use crate::app::{
    AppInfo, AppState, AutoStartState, DaemonState,
    AppError, AppR<PERSON>ult, AppManagerConfig
};
use crate::app::script::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ScriptExecutor};
use crate::daemon::{<PERSON><PERSON>anager, DaemonConfig};
use crate::package::PackageManager;
use crate::package::opkg::Opkg;

// Package management will be implemented later

use std::collections::HashMap;
use std::path::PathBuf;
use std::process::{Command, Stdio};
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::sync::{mpsc, RwLock, Mutex};
use tokio::time::timeout;
use sysinfo::{System, Pid};
use log::{debug, info, warn, error};

/// Main application manager
pub struct AppManager {
    /// Configuration
    config: AppManagerConfig,
    /// Application information cache
    apps: Arc<Mutex<HashMap<String, AppInfo>>>,
    /// Script parser
    script_parser: ScriptParser,
    /// Script executor
    script_executor: ScriptExecutor,
    /// Daemon manager for process monitoring
    daemon_manager: Option<Arc<RwLock<DaemonManager>>>,
    /// Package manager for installation/removal (placeholder for now)
    package_manager: Arc<Box<dyn PackageManager>>,
    /// System information
    system: Arc<RwLock<System>>,
    /// Last directory modification time
    last_scan: Instant,
    /// Command receiver for external control
    command_rx: Option<mpsc::UnboundedReceiver<crate::app::AppCommand>>,
}

impl AppManager {
    /// Create a new application manager
    pub fn new(config: AppManagerConfig) -> AppResult<Self> {
        let script_parser = ScriptParser::new()?;
        let script_executor = ScriptExecutor::new(config.operation_timeout);
        
        // Initialize system information
        let system = Arc::new(RwLock::new(System::new_all()));
        
        // Initialize daemon manager if needed
        let daemon_manager = 
            Some(Arc::new(RwLock::new(
                crate::daemon::utils::create_default_manager()
                    .map_err(|e| AppError::SystemError(format!("Failed to create daemon manager: {}", e)))?
            )));

        // Initialize package manager placeholder
        let package_manager = Some(Box::new(Opkg::new()));

        Ok(Self {
            config,
            apps: Arc::new(Mutex::new(HashMap::new())),
            script_parser,
            script_executor,
            daemon_manager,
            package_manager,
            system,
            last_scan: Instant::now(),
            command_rx: None,
        })
    }

    /// Create with default configuration
    pub fn with_default_config() -> AppResult<Self> {
        Self::new(AppManagerConfig::default())
    }

    /// Set command receiver for external control
    pub fn set_command_receiver(&mut self, rx: mpsc::UnboundedReceiver<crate::app::AppCommand>) {
        self.command_rx = Some(rx);
    }

    /// Initialize the application manager
    pub async fn initialize(&mut self) -> AppResult<()> {
        info!("Initializing application manager");
        
        // Create directories if they don't exist
        self.ensure_directories().await?;
        
        // Scan for applications
        self.scan_applications().await?;
        
        // Update system information
        self.update_system_info().await;
        
        info!("Application manager initialized successfully");
        Ok(())
    }

    /// Ensure required directories exist
    async fn ensure_directories(&self) -> AppResult<()> {
        use tokio::fs;
        
        for dir in [&self.config.script_path, &self.config.link_path] {
            if !dir.exists() {
                fs::create_dir_all(dir).await
                    .map_err(|e| AppError::IoError(e))?;
                info!("Created directory: {}", dir.display());
            }
        }
        
        Ok(())
    }

    /// Scan for applications in the script directory
    pub async fn scan_applications(&mut self) -> AppResult<()> {
        debug!("Scanning applications in {}", self.config.script_path.display());
        
        let scripts = self.script_parser.find_scripts(&self.config.script_path)?;
        let mut apps = self.apps.lock().await;
        
        // Clear existing apps that are no longer present
        let script_names: std::collections::HashSet<String> = scripts.iter()
            .filter_map(|s| s.path.file_stem().and_then(|n| n.to_str()))
            .map(|s| {
                // Remove stage prefix if present
                if let Some(captures) = regex::Regex::new(r"^S\d+(.*)").unwrap().captures(s) {
                    captures.get(1).unwrap().as_str().to_string()
                } else {
                    s.to_string()
                }
            })
            .collect();
        
        apps.retain(|name, _| script_names.contains(name));
        
        // Add or update applications
        for script in scripts {
            let app_name = script.path.file_stem()
                .and_then(|n| n.to_str())
                .unwrap_or("unknown")
                .to_string();
            
            // Remove stage prefix if present
            let app_name = if let Some(captures) = regex::Regex::new(r"^S\d+(.*)").unwrap().captures(&app_name) {
                captures.get(1).unwrap().as_str().to_string()
            } else {
                app_name
            };
            
            let mut app_info = apps.get(&app_name).cloned()
                .unwrap_or_else(|| AppInfo::new(app_name.clone()));
            
            // Update script information
            app_info.script = Some(script);
            // app_info.installed = true;
            
            // Check auto-start status
            let link_path = self.config.link_path.join(
                app_info.script.as_ref().unwrap().path.file_name().unwrap()
            );
            app_info.auto_start = if link_path.exists() {
                AutoStartState::Enabled
            } else {
                AutoStartState::Disabled
            };
            
            // Update running state
            self.update_app_state(&mut app_info).await?;
            
            apps.insert(app_name, app_info);
        }
        
        self.last_scan = Instant::now();
        info!("Scanned {} applications", apps.len());
        Ok(())
    }

    /// Update application running state
    async fn update_app_state(&self, app_info: &mut AppInfo) -> AppResult<()> {
        
        Ok(())
    }

    /// Update resource usage information for an application
    async fn update_resource_usage(&self, app_info: &mut AppInfo, pid: u32) {}

    /// Update system information
    async fn update_system_info(&self) {
        let mut system = self.system.write().await;
        system.refresh_all();
    }

    /// Start an application
    pub async fn start_application(&mut self, name: &str, debug_mode: bool) -> AppResult<()> {
        info!("Starting application: {}", name);
        
        let mut apps = self.apps.lock().await;
        let app_info = apps.get_mut(name)
            .ok_or_else(|| AppError::NotFound(name.to_string()))?;
        
        // Check if already running
        if app_info.is_running() {
            return Err(AppError::AlreadyRunning(name.to_string()));
        }
        
        // Get script information
        let script = app_info.script.as_ref()
            .ok_or_else(|| AppError::ConfigError(format!("No script found for application '{}'", name)))?
            .clone();
        
        // Set state to starting
        app_info.set_state(AppState::Starting);
        
        // Execute start script
        let start_result = timeout(
            Duration::from_secs(self.config.operation_timeout),
            async {
                self.script_executor.execute_script(&script, "start")
            }
        ).await;
        
        match start_result {
            Ok(Ok(output)) => {
                if output.status.success() {
                    // Wait for process to actually start
                    self.wait_for_start(app_info, name).await?;
                    
                    // Start daemon monitoring if not in debug mode
                    if !debug_mode && app_info.daemon_state == DaemonState::Enabled {
                        self.start_daemon_monitoring(name).await?;
                    }
                    
                    info!("Successfully started application: {}", name);
                    Ok(())
                } else {
                    app_info.set_state(AppState::Failed);
                    let stderr = String::from_utf8_lossy(&output.stderr);
                    Err(AppError::StartFailed(name.to_string(), stderr.to_string()))
                }
            }
            Ok(Err(e)) => {
                app_info.set_state(AppState::Failed);
                Err(e)
            }
            Err(_) => {
                app_info.set_state(AppState::Failed);
                Err(AppError::Timeout(name.to_string(), "start".to_string()))
            }
        }
    }

    /// Wait for application to start and update state
    async fn wait_for_start(&self, app_info: &mut AppInfo, name: &str) -> AppResult<()> {
        let start_time = Instant::now();
        let timeout_duration = Duration::from_secs(5);
        
        while start_time.elapsed() < timeout_duration {
            self.update_app_state(app_info).await?;
            
            if app_info.is_running() {
                return Ok(());
            }
            
            tokio::time::sleep(Duration::from_millis(100)).await;
        }
        
        Err(AppError::Timeout(name.to_string(), "start verification".to_string()))
    }

    /// Start daemon monitoring for an application
    async fn start_daemon_monitoring(&self, name: &str) -> AppResult<()> {
        if let Some(daemon_manager) = &self.daemon_manager {
            let apps = self.apps.lock().await;
            if let Some(app_info) = apps.get(name) {
                if let Some(pid) = app_info.resource_usage.pid {
                    let mut dm = daemon_manager.write().await;
                    dm.add_task(name.to_string(), pid, None)
                        .map_err(|e| AppError::SystemError(format!("Failed to add daemon task: {}", e)))?;
                    dm.start_task(name)
                        .map_err(|e| AppError::SystemError(format!("Failed to start daemon task: {}", e)))?;
                    debug!("Started daemon monitoring for {}", name);
                }
            }
        }
        Ok(())
    }

    /// Stop an application
    pub async fn stop_application(&mut self, name: &str, force: bool) -> AppResult<()> {
        info!("Stopping application: {}", name);

        let mut apps = self.apps.lock().await;
        let app_info = apps.get_mut(name)
            .ok_or_else(|| AppError::NotFound(name.to_string()))?;

        // Check if already stopped
        if !app_info.is_running() {
            return Err(AppError::NotRunning(name.to_string()));
        }

        // Stop daemon monitoring first
        self.stop_daemon_monitoring(name).await?;

        // Set state to stopping
        app_info.set_state(AppState::Stopping);

        if force {
            // Force kill the process
            if let Some(pid) = app_info.resource_usage.pid {
                let kill_result = Command::new("kill")
                    .args(["-9", &pid.to_string()])
                    .output();

                match kill_result {
                    Ok(output) if output.status.success() => {
                        app_info.set_state(AppState::Stopped);
                        app_info.resource_usage.pid = None;
                        info!("Force killed application: {}", name);
                        return Ok(());
                    }
                    _ => {
                        return Err(AppError::StopFailed(name.to_string(), "Force kill failed".to_string()));
                    }
                }
            } else {
                return Err(AppError::StopFailed(name.to_string(), "No PID found for force kill".to_string()));
            }
        } else {
            // Use script to stop gracefully
            if let Some(script) = &app_info.script {
                let stop_result = timeout(
                    Duration::from_secs(self.config.operation_timeout),
                    async {
                        self.script_executor.execute_script(script, "stop")
                    }
                ).await;

                match stop_result {
                    Ok(Ok(output)) => {
                        if output.status.success() {
                            // Wait for process to actually stop
                            self.wait_for_stop(app_info, name).await?;
                            info!("Successfully stopped application: {}", name);
                            Ok(())
                        } else {
                            app_info.set_state(AppState::Failed);
                            let stderr = String::from_utf8_lossy(&output.stderr);
                            Err(AppError::StopFailed(name.to_string(), stderr.to_string()))
                        }
                    }
                    Ok(Err(e)) => {
                        app_info.set_state(AppState::Failed);
                        Err(e)
                    }
                    Err(_) => {
                        app_info.set_state(AppState::Failed);
                        Err(AppError::Timeout(name.to_string(), "stop".to_string()))
                    }
                }
            } else {
                Err(AppError::ConfigError(format!("No script found for application '{}'", name)))
            }
        }
    }

    /// Wait for application to stop and update state
    async fn wait_for_stop(&self, app_info: &mut AppInfo, name: &str) -> AppResult<()> {
        let start_time = Instant::now();
        let timeout_duration = Duration::from_secs(5);

        while start_time.elapsed() < timeout_duration {
            self.update_app_state(app_info).await?;

            if !app_info.is_running() {
                app_info.set_state(AppState::Stopped);
                // app_info.resource_usage.pid = None;
                return Ok(());
            }

            tokio::time::sleep(Duration::from_millis(100)).await;
        }

        Err(AppError::Timeout(name.to_string(), "stop verification".to_string()))
    }

    /// Stop daemon monitoring for an application
    async fn stop_daemon_monitoring(&self, name: &str) -> AppResult<()> {
        if let Some(daemon_manager) = &self.daemon_manager {
            let mut dm = daemon_manager.write().await;
            if dm.has_task(name) {
                dm.stop_task(name).await
                    .map_err(|e| AppError::SystemError(format!("Failed to stop daemon task: {}", e)))?;
                dm.remove_task(name).await
                    .map_err(|e| AppError::SystemError(format!("Failed to remove daemon task: {}", e)))?;
                debug!("Stopped daemon monitoring for {}", name);
            }
        }
        Ok(())
    }

    /// Enable auto-start for an application
    pub async fn enable_auto_start(&mut self, name: &str) -> AppResult<()> {
        info!("Enabling auto-start for application: {}", name);

        if name == "all" {
            return self.enable_all_auto_start().await;
        }

        let mut apps = self.apps.lock().await;
        let app_info = apps.get_mut(name)
            .ok_or_else(|| AppError::NotFound(name.to_string()))?;

        if let Some(script) = &app_info.script {
            let script_filename = script.path.file_name()
                .ok_or_else(|| AppError::ConfigError("Invalid script path".to_string()))?;

            let link_path = self.config.link_path.join(script_filename);

            if !link_path.exists() {
                std::os::unix::fs::symlink(&script.path, &link_path)
                    .map_err(|e| AppError::IoError(e))?;

                app_info.set_auto_start(AutoStartState::Enabled);
                info!("Enabled auto-start for {}", name);
            }
        } else {
            return Err(AppError::ConfigError(format!("No script found for application '{}'", name)));
        }

        Ok(())
    }

    /// Enable auto-start for all applications
    async fn enable_all_auto_start(&mut self) -> AppResult<()> {
        let apps = self.apps.lock().await;
        let app_names: Vec<String> = apps.keys().cloned().collect();
        drop(apps);

        for app_name in app_names {
            if let Err(e) = self.enable_auto_start(&app_name).await {
                warn!("Failed to enable auto-start for {}: {}", app_name, e);
            }
        }

        Ok(())
    }

    /// Disable auto-start for an application
    pub async fn disable_auto_start(&mut self, name: &str) -> AppResult<()> {
        info!("Disabling auto-start for application: {}", name);

        if name == "all" {
            return self.disable_all_auto_start().await;
        }

        let mut apps = self.apps.lock().await;
        let app_info = apps.get_mut(name)
            .ok_or_else(|| AppError::NotFound(name.to_string()))?;

        if let Some(script) = &app_info.script {
            let script_filename = script.path.file_name()
                .ok_or_else(|| AppError::ConfigError("Invalid script path".to_string()))?;

            let link_path = self.config.link_path.join(script_filename);

            if link_path.exists() {
                std::fs::remove_file(&link_path)
                    .map_err(|e| AppError::IoError(e))?;

                app_info.set_auto_start(AutoStartState::Disabled);
                info!("Disabled auto-start for {}", name);
            }
        } else {
            return Err(AppError::ConfigError(format!("No script found for application '{}'", name)));
        }

        Ok(())
    }

    /// Disable auto-start for all applications
    async fn disable_all_auto_start(&mut self) -> AppResult<()> {
        use tokio::fs;

        let mut entries = fs::read_dir(&self.config.link_path).await
            .map_err(|e| AppError::IoError(e))?;

        while let Some(entry) = entries.next_entry().await.map_err(|e| AppError::IoError(e))? {
            if let Err(e) = fs::remove_file(entry.path()).await {
                warn!("Failed to remove auto-start link {}: {}", entry.path().display(), e);
            }
        }

        // Update all app states
        let mut apps = self.apps.lock().await;
        for app_info in apps.values_mut() {
            app_info.set_auto_start(AutoStartState::Disabled);
        }

        info!("Disabled auto-start for all applications");
        Ok(())
    }

    /// Get application version
    pub async fn get_application_version(&self, name: &str) -> AppResult<String> {
        let apps = self.apps.lock().await;
        let app_info = apps.get(name)
            .ok_or_else(|| AppError::NotFound(name.to_string()))?;

        if let Some(script) = &app_info.script {
            self.script_executor.get_version(script, name)
        } else {
            Ok(app_info.version.clone())
        }
    }

    /// Get application state information
    pub async fn get_application_state(&self, name: &str) -> AppResult<AppInfo> {
        let mut apps = self.apps.lock().await;
        let app_info = apps.get_mut(name)
            .ok_or_else(|| AppError::NotFound(name.to_string()))?;

        // Update current state
        self.update_app_state(app_info).await?;

        Ok(app_info.clone())
    }

    /// List all applications
    pub async fn list_applications(&mut self, installed_only: bool, running_only: bool) -> AppResult<Vec<AppInfo>> {
        // Refresh application list
        self.scan_applications().await?;

        let mut apps = self.apps.lock().await;
        let mut result = Vec::new();

        for app_info in apps.values_mut() {
            // Update current state
            self.update_app_state(app_info).await?;

            // Apply filters
            // if installed_only && !app_info.installed {
            //     continue;
            // }

            if running_only && !app_info.is_running() {
                continue;
            }

            result.push(app_info.clone());
        }

        // Sort by name
        result.sort_by(|a, b| a.name.cmp(&b.name));

        Ok(result)
    }

    /// Set daemon state for an application
    pub async fn set_daemon_state(&mut self, name: &str, state: DaemonState) -> AppResult<()> {
        let mut apps = self.apps.lock().await;
        let app_info = apps.get_mut(name)
            .ok_or_else(|| AppError::NotFound(name.to_string()))?;

        let old_state = app_info.daemon_state.clone();
        app_info.set_daemon_state(state.clone());

        match (old_state, state) {
            (DaemonState::Disabled, DaemonState::Enabled) => {
                if app_info.is_running() {
                    self.start_daemon_monitoring(name).await?;
                }
            }
            (DaemonState::Enabled, DaemonState::Disabled) => {
                self.stop_daemon_monitoring(name).await?;
            }
            (DaemonState::Enabled, DaemonState::Suspended) => {
                // TODO: Suspend daemon monitoring
                debug!("Suspending daemon monitoring for {}", name);
            }
            (DaemonState::Suspended, DaemonState::Enabled) => {
                // TODO: Resume daemon monitoring
                debug!("Resuming daemon monitoring for {}", name);
            }
            _ => {
                // No action needed
            }
        }

        info!("Set daemon state for {} to {:?}", name, app_info.daemon_state);
        Ok(())
    }

    /// Start all auto-start applications
    pub async fn start_auto_start_applications(&mut self) -> AppResult<()> {
        info!("Starting auto-start applications");

        // Get applications with auto-start enabled, sorted by stage
        let apps = self.apps.lock().await;
        let mut auto_start_apps: Vec<(String, u32)> = apps.iter()
            .filter(|(_, app)| app.is_auto_start_enabled())
            .map(|(name, app)| (name.clone(), app.get_stage()))
            .collect();
        drop(apps);

        // Sort by stage number
        auto_start_apps.sort_by_key(|(_, stage)| *stage);

        // Start applications in order
        for (name, _) in auto_start_apps {
            match self.start_application(&name, false).await {
                Ok(()) => {
                    info!("Auto-started application: {}", name);
                }
                Err(e) => {
                    error!("Failed to auto-start application {}: {}", name, e);
                }
            }

            // Small delay between starts
            tokio::time::sleep(Duration::from_millis(500)).await;
        }

        Ok(())
    }

    /// Stop all running applications
    pub async fn stop_all_applications(&mut self, force: bool) -> AppResult<()> {
        info!("Stopping all running applications");

        // Get running applications, sorted by reverse stage order
        let apps = self.apps.lock().await;
        let mut running_apps: Vec<(String, u32)> = apps.iter()
            .filter(|(_, app)| app.is_running())
            .map(|(name, app)| (name.clone(), app.get_stage()))
            .collect();
        drop(apps);

        // Sort by reverse stage number (stop in reverse order)
        running_apps.sort_by_key(|(_, stage)| std::cmp::Reverse(*stage));

        // Stop applications in reverse order
        for (name, _) in running_apps {
            match self.stop_application(&name, force).await {
                Ok(()) => {
                    info!("Stopped application: {}", name);
                }
                Err(e) => {
                    error!("Failed to stop application {}: {}", name, e);
                }
            }

            // Small delay between stops
            tokio::time::sleep(Duration::from_millis(200)).await;
        }

        Ok(())
    }

    /// Update resource usage for all running applications
    pub async fn update_all_resource_usage(&mut self) -> AppResult<()> {
        self.update_system_info().await;

        let mut apps = self.apps.lock().await;
        for app_info in apps.values_mut() {
            // if let Some(pid) = app_info.resource_usage.pid {
            //     self.update_resource_usage(app_info, pid).await;
            // }
        }

        Ok(())
    }

    /// Get configuration
    pub fn get_config(&self) -> &AppManagerConfig {
        &self.config
    }

    /// Update configuration
    pub fn update_config(&mut self, new_config: AppManagerConfig) {
        self.config = new_config;
        self.script_executor = ScriptExecutor::new(self.config.operation_timeout);
    }

    /// Check if rescan is needed based on directory modification time
    pub async fn needs_rescan(&self) -> bool {
        // Check if enough time has passed since last scan
        if self.last_scan.elapsed() < Duration::from_secs(self.config.monitor_interval) {
            return false;
        }

        // Check if script directory has been modified
        if let Ok(metadata) = std::fs::metadata(&self.config.script_path) {
            if let Ok(modified) = metadata.modified() {
                if let Ok(duration) = modified.duration_since(SystemTime::UNIX_EPOCH) {
                    let last_scan_duration = self.last_scan.elapsed();
                    return duration.as_secs() > (SystemTime::now()
                        .duration_since(SystemTime::UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs() - last_scan_duration.as_secs());
                }
            }
        }

        true
    }

    /// Run the main application manager loop
    pub async fn run(&mut self) -> AppResult<()> {
        info!("Starting application manager main loop");

        let mut update_interval = tokio::time::interval(Duration::from_secs(self.config.monitor_interval));

        loop {
            tokio::select! {
                // Handle periodic updates
                _ = update_interval.tick() => {
                    if self.needs_rescan().await {
                        if let Err(e) = self.scan_applications().await {
                            error!("Failed to scan applications: {}", e);
                        }
                    }

                    if let Err(e) = self.update_all_resource_usage().await {
                        error!("Failed to update resource usage: {}", e);
                    }
                }

                // Handle external commands
                command = async {
                    if let Some(ref mut rx) = self.command_rx {
                        rx.recv().await
                    } else {
                        std::future::pending().await
                    }
                } => {
                    if let Some(cmd) = command {
                        if let Err(e) = self.handle_command(cmd).await {
                            error!("Failed to handle command: {}", e);
                        }
                    } else {
                        // Channel closed, exit
                        break;
                    }
                }
            }
        }

        info!("Application manager main loop ended");
        Ok(())
    }

    /// Handle external commands
    async fn handle_command(&mut self, _command: crate::app::AppCommand) -> AppResult<()> {
        // TODO: Implement command handling
        // This would process commands received via D-Bus or other interfaces
        Ok(())
    }

    /// Install packages
    pub async fn install_packages(&mut self, packages: Vec<String>, _dest: Option<PathBuf>) -> AppResult<()> {
        info!("Installing packages: {:?}", packages);

        // TODO: Implement actual package installation
        // For now, return a placeholder error
        Err(AppError::SystemError("Package installation not yet implemented".to_string()))
    }

    /// Remove packages
    pub async fn remove_packages(&mut self, packages: Vec<String>) -> AppResult<()> {
        info!("Removing packages: {:?}", packages);

        // TODO: Implement actual package removal
        // For now, return a placeholder error
        Err(AppError::SystemError("Package removal not yet implemented".to_string()))
    }

    /// Update package index
    pub async fn update_package_index(&self) -> AppResult<Vec<String>> {
        info!("Updating package index");

        // TODO: Implement actual package index update
        Err(AppError::SystemError("Package index update not yet implemented".to_string()))
    }

    /// List installed packages
    pub async fn list_installed_packages(&self) -> AppResult<Vec<String>> {
        // TODO: Implement actual package listing
        Err(AppError::SystemError("Package listing not yet implemented".to_string()))
    }

    /// List upgradable packages
    pub async fn list_upgradable_packages(&self) -> AppResult<Vec<String>> {
        // TODO: Implement actual upgradable package listing
        Err(AppError::SystemError("Upgradable package listing not yet implemented".to_string()))
    }

    /// Upgrade packages
    pub async fn upgrade_packages(&mut self, packages: Vec<String>) -> AppResult<()> {
        info!("Upgrading packages: {:?}", packages);

        // TODO: Implement actual package upgrade
        Err(AppError::SystemError("Package upgrade not yet implemented".to_string()))
    }
}
