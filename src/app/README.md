# Application Manager Module

这是一个完整的应用程序管理模块，基于设计文档实现了应用的启停、自启动管理、守护进程监控、包管理等功能。

## 模块结构

```
src/app/
├── mod.rs          # 模块入口和配置
├── app.rs          # 核心数据结构和错误类型
├── manager.rs      # 应用管理器主要实现
├── script.rs       # 应用配置脚本解析和执行
├── commands.rs     # 命令行接口定义
├── dbus.rs         # D-Bus接口实现
├── tests.rs        # 单元测试
└── README.md       # 本文档
```

## 核心功能

### 1. 应用信息管理 (app.rs)

- **AppInfo**: 完整的应用信息结构，包含：
  - 基本信息：名称、版本、描述、架构
  - 状态信息：运行状态、自启动状态、守护进程状态
  - 资源使用：CPU、内存、运行时间
  - 配置信息：脚本路径、依赖关系、配置文件

- **状态枚举**：
  - `AppState`: 运行状态 (Stopped, Running, Starting, Stopping, Failed, Unknown)
  - `AutoStartState`: 自启动状态 (Disabled, Enabled)
  - `DaemonState`: 守护进程状态 (Disabled, Enabled, Suspended)

- **错误处理**: 完整的错误类型定义，支持错误分类和恢复策略

### 2. 应用管理器 (manager.rs)

- **AppManager**: 主要的应用管理类，提供：
  - 应用扫描和状态更新
  - 应用启停控制
  - 自启动管理
  - 守护进程集成
  - 资源监控

- **核心方法**：
  - `start_application()`: 启动应用
  - `stop_application()`: 停止应用
  - `enable_auto_start()`: 启用自启动
  - `disable_auto_start()`: 禁用自启动
  - `list_applications()`: 列出应用
  - `get_application_state()`: 获取应用状态

### 3. 脚本管理 (script.rs)

- **ScriptParser**: 解析应用配置脚本
  - 支持S<数字><名称>格式的脚本文件
  - 解析EXECUTE、ARGS、VERSION_CMD等变量
  - 检测自定义start/stop函数

- **ScriptExecutor**: 执行应用脚本
  - 支持start、stop、version等操作
  - 自动设置可执行权限
  - 超时控制

### 4. 命令行接口 (commands.rs)

使用clap实现完整的命令行接口：

```bash
# 基本操作
app list [--installed] [--running] [--detailed]
app start <name> [--debug]
app stop <name> [--force]
app enable <name>
app disable <name>
app ver <name>
app state <name> [--format json|yaml|table]

# 包管理 (待实现)
app install [--dest <path>] <packages...>
app remove <packages...>
app update
app upgrade [packages...]
app list-upgradable

# 守护进程管理
app daemon <name> start|stop|suspend|resume|status
app daemon <name> limit [--cpu <percent>] [--memory <mb>]
```

### 5. D-Bus接口 (dbus.rs)

实现完整的D-Bus接口，符合设计文档规范：

- **服务名**: `mgc.platform.AppManager1`
- **对象路径**: `/mgc/platform/AppManager1`
- **接口**: `mgc.platform.AppManager1`

**主要方法**：
- `Run(apps: Vec<(Variant, bool)>)`: 批量启停应用
- `SetAutoStart(apps: Vec<(Variant, bool)>)`: 批量设置自启动
- `Daemonize(apps: Vec<(Variant, bool)>)`: 批量设置守护进程
- `State(app_ids: Vec<Variant>) -> Vec<String>`: 获取应用状态
- `Ver(app_ids: Vec<Variant>) -> Vec<String>`: 获取应用版本
- `Install(root: String, packages: Vec<String>)`: 安装包
- `Remove(packages: Vec<String>)`: 移除包
- `Update() -> Vec<String>`: 更新包索引
- `Upgrade(packages: Vec<String>)`: 升级包

### 6. 测试套件 (tests.rs)

包含完整的单元测试和集成测试：

- 数据结构测试
- 脚本解析测试
- 应用管理器测试
- 命令行接口测试
- 错误处理测试

## 配置

### AppManagerConfig

```rust
let config = AppManagerConfig::new()
    .with_script_path("/usr/lib/app-init.d")
    .with_link_path("/etc/app-init.d")
    .with_install_root("/usr/local")
    .with_timeout(30)
    .with_monitor_interval(10)
    .with_verbose(true)
    .with_dbus(true);
```

## 使用示例

### 基本使用

```rust
use app::{AppManager, AppManagerConfig};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 创建配置
    let config = AppManagerConfig::default();
    
    // 创建管理器
    let mut manager = AppManager::new(config)?;
    manager.initialize().await?;
    
    // 启动应用
    manager.start_application("myapp", false).await?;
    
    // 启用自启动
    manager.enable_auto_start("myapp").await?;
    
    // 获取状态
    let app_info = manager.get_application_state("myapp").await?;
    println!("App state: {}", app_info.state);
    
    Ok(())
}
```

### 守护模式

```rust
// 启动守护模式，同时提供D-Bus接口
let manager_arc = Arc::new(RwLock::new(manager));

// 启动D-Bus服务
let dbus_manager = manager_arc.clone();
tokio::spawn(async move {
    AppManagerDBus::start_service(dbus_manager).await
});

// 运行主循环
let mut mgr = manager_arc.write().await;
mgr.run().await?;
```

## 实现状态

### 已完成功能 ✅

- [x] 核心数据结构和错误处理
- [x] 应用脚本解析和执行
- [x] 应用启停管理
- [x] 自启动管理
- [x] 命令行接口
- [x] D-Bus接口框架
- [x] 守护进程集成框架
- [x] 测试套件
- [x] 错误处理和日志记录

### 待实现功能 🚧

- [ ] 包管理功能 (install/remove/update/upgrade)
- [ ] 守护进程具体实现
- [ ] 状态通知和信号机制
- [ ] 配置文件加载
- [ ] 性能优化

### 已知限制 ⚠️

1. **包管理**: 当前为占位符实现，需要集成实际的opkg功能
2. **守护进程**: 框架已就绪，但具体监控逻辑需要完善
3. **D-Bus**: 基本接口已实现，但需要更多测试
4. **错误恢复**: 基本错误处理已实现，但恢复策略需要完善

## 下一步实现

1. **完善包管理**: 集成opkg模块，实现实际的包安装/移除功能
2. **守护进程监控**: 实现进程监控、资源限制、自动重启等功能
3. **状态通知**: 实现D-Bus信号发送，支持状态变化通知
4. **配置管理**: 支持从配置文件加载设置
5. **性能优化**: 优化扫描频率、缓存策略等

## 架构特点

- **模块化设计**: 各功能模块独立，便于测试和维护
- **异步支持**: 全面使用tokio异步运行时
- **错误处理**: 完整的错误类型和处理策略
- **可配置**: 支持灵活的配置选项
- **可测试**: 完整的测试覆盖
- **可扩展**: 预留扩展接口，便于后续功能添加

这个实现为应用管理提供了一个坚实的基础，可以根据具体需求进一步完善和扩展。
