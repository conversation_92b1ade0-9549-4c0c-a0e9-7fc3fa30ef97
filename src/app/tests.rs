#[cfg(test)]
mod tests {
    use crate::app::{AppInfo, AppState, AutoStartState, DaemonState, AppManagerConfig, AppManager};
    use std::path::PathBuf;
    use tempfile::TempDir;
    use tokio;

    /// Create a temporary directory for testing
    fn create_test_dir() -> TempDir {
        tempfile::tempdir().expect("Failed to create temp dir")
    }

    /// Create a test script file
    async fn create_test_script(dir: &std::path::Path, name: &str, content: &str) -> std::io::Result<PathBuf> {
        let script_path = dir.join(name);
        tokio::fs::write(&script_path, content).await?;
        
        // Make executable
        use std::os::unix::fs::PermissionsExt;
        let mut perms = tokio::fs::metadata(&script_path).await?.permissions();
        perms.set_mode(0o755);
        tokio::fs::set_permissions(&script_path, perms).await?;
        
        Ok(script_path)
    }

    #[test]
    fn test_app_info_creation() {
        let app = AppInfo::new("test_app".to_string());
        assert_eq!(app.name, "test_app");
        assert_eq!(app.state, AppState::Stopped);
        assert_eq!(app.auto_start, AutoStartState::Disabled);
        assert_eq!(app.daemon_state, DaemonState::Disabled);
        assert!(!app.is_running());
        assert!(!app.is_auto_start_enabled());
        assert!(!app.is_daemon_enabled());
    }

    #[test]
    fn test_app_info_state_transitions() {
        let mut app = AppInfo::new("test_app".to_string());
        
        // Test state changes
        app.set_state(AppState::Running);
        assert_eq!(app.state, AppState::Running);
        assert!(app.is_running());
        
        app.set_auto_start(AutoStartState::Enabled);
        assert_eq!(app.auto_start, AutoStartState::Enabled);
        assert!(app.is_auto_start_enabled());
        
        app.set_daemon_state(DaemonState::Enabled);
        assert_eq!(app.daemon_state, DaemonState::Enabled);
        assert!(app.is_daemon_enabled());
    }

    #[test]
    fn test_app_info_dbus_conversion() {
        let mut app = AppInfo::new("test_app".to_string());
        app.set_state(AppState::Running);
        app.set_auto_start(AutoStartState::Enabled);
        app.set_daemon_state(DaemonState::Enabled);
        
        let dbus_state = app.to_dbus_state();
        assert_eq!(dbus_state, (1, 1, 1)); // Running, Enabled, Enabled
        
        let mut app2 = AppInfo::new("test_app2".to_string());
        app2.from_dbus_state(dbus_state);
        assert_eq!(app2.state, AppState::Running);
        assert_eq!(app2.auto_start, AutoStartState::Enabled);
        assert_eq!(app2.daemon_state, DaemonState::Enabled);
    }

    #[tokio::test]
    async fn test_script_parser() {
        let temp_dir = create_test_dir();
        let script_content = r#"#!/bin/bash
EXECUTE=myapp
ARGS="--config /etc/myapp.conf"
VERSION_CMD="myapp --version"

start() {
    echo "Starting myapp"
    $EXECUTE $ARGS &
}

stop() {
    echo "Stopping myapp"
    killall myapp
}
"#;

        let script_path = create_test_script(temp_dir.path(), "S80myapp", script_content)
            .await
            .expect("Failed to create test script");

        let parser = crate::app::script::ScriptParser::new().expect("Failed to create parser");
        let script = parser.parse_script(&script_path).expect("Failed to parse script");

        assert_eq!(script.stage, Some(80));
        assert_eq!(script.executable, Some("myapp".to_string()));
        assert_eq!(script.args, Some("--config /etc/myapp.conf".to_string()));
        assert_eq!(script.version_cmd, Some("myapp --version".to_string()));
        assert!(script.has_custom_functions);
    }

    #[tokio::test]
    async fn test_app_manager_config() {
        let temp_dir = create_test_dir();
        
        let config = AppManagerConfig::new()
            .with_script_path(temp_dir.path().join("scripts"))
            .with_link_path(temp_dir.path().join("links"))
            .with_timeout(60)
            .with_verbose(true);

        assert_eq!(config.operation_timeout, 60);
        assert!(config.verbose);
        assert_eq!(config.script_path, temp_dir.path().join("scripts"));
        assert_eq!(config.link_path, temp_dir.path().join("links"));
    }

    #[tokio::test]
    async fn test_app_manager_initialization() {
        let temp_dir = create_test_dir();
        
        let config = AppManagerConfig::new()
            .with_script_path(temp_dir.path().join("scripts"))
            .with_link_path(temp_dir.path().join("links"))
            .with_enable_dbus(false); // Disable D-Bus for testing

        let mut manager = AppManager::new(config).expect("Failed to create manager");
        
        // This should create the directories
        manager.initialize().await.expect("Failed to initialize manager");
        
        assert!(temp_dir.path().join("scripts").exists());
        assert!(temp_dir.path().join("links").exists());
    }

    #[tokio::test]
    async fn test_app_manager_scan_applications() {
        let temp_dir = create_test_dir();
        let scripts_dir = temp_dir.path().join("scripts");
        let links_dir = temp_dir.path().join("links");
        
        // Create directories
        tokio::fs::create_dir_all(&scripts_dir).await.expect("Failed to create scripts dir");
        tokio::fs::create_dir_all(&links_dir).await.expect("Failed to create links dir");
        
        // Create test scripts
        let script1_content = r#"#!/bin/bash
EXECUTE=app1
start() { echo "start app1"; }
stop() { echo "stop app1"; }
"#;
        
        let script2_content = r#"#!/bin/bash
EXECUTE=app2
ARGS="--daemon"
start() { echo "start app2"; }
stop() { echo "stop app2"; }
"#;

        create_test_script(&scripts_dir, "S10app1", script1_content).await.expect("Failed to create script1");
        create_test_script(&scripts_dir, "S20app2", script2_content).await.expect("Failed to create script2");
        
        // Create auto-start link for app1
        std::os::unix::fs::symlink(
            scripts_dir.join("S10app1"),
            links_dir.join("S10app1")
        ).expect("Failed to create symlink");

        let config = AppManagerConfig::new()
            .with_script_path(scripts_dir)
            .with_link_path(links_dir)
            .with_enable_dbus(false);

        let mut manager = AppManager::new(config).expect("Failed to create manager");
        manager.initialize().await.expect("Failed to initialize manager");

        let apps = manager.list_applications(true, false).await.expect("Failed to list applications");
        
        assert_eq!(apps.len(), 2);
        
        // Find app1 and app2
        let app1 = apps.iter().find(|app| app.name == "app1").expect("app1 not found");
        let app2 = apps.iter().find(|app| app.name == "app2").expect("app2 not found");
        
        assert_eq!(app1.auto_start, AutoStartState::Enabled);
        assert_eq!(app2.auto_start, AutoStartState::Disabled);
        assert_eq!(app1.get_stage(), 10);
        assert_eq!(app2.get_stage(), 20);
    }

    #[test]
    fn test_app_error_categories() {
        use crate::app::AppError;
        
        let error1 = AppError::NotFound("test".to_string());
        assert_eq!(error1.category(), "not_found");
        assert!(!error1.is_recoverable());
        assert!(!error1.requires_user_intervention());
        
        let error2 = AppError::PermissionDenied("test".to_string());
        assert_eq!(error2.category(), "permission_denied");
        assert!(!error2.is_recoverable());
        assert!(error2.requires_user_intervention());
        
        let error3 = AppError::Timeout("test".to_string(), "start".to_string());
        assert_eq!(error3.category(), "timeout");
        assert!(error3.is_recoverable());
        assert!(!error3.requires_user_intervention());
    }

    #[test]
    fn test_app_command_parsing() {
        use crate::app::commands::{AppCommand, AppSubCommand};
        
        // Test list command
        let cmd = AppCommand::parse_from_iter(&["app", "list", "--installed"]);
        match cmd.command {
            AppSubCommand::List { installed, running, detailed } => {
                assert!(installed);
                assert!(!running);
                assert!(!detailed);
            }
            _ => panic!("Expected List command"),
        }
        
        // Test start command
        let cmd = AppCommand::parse_from_iter(&["app", "start", "myapp", "--debug"]);
        match cmd.command {
            AppSubCommand::Start { name, debug, args } => {
                assert_eq!(name, "myapp");
                assert!(debug);
                assert!(args.is_none());
            }
            _ => panic!("Expected Start command"),
        }
    }

    #[test]
    fn test_command_properties() {
        use crate::app::commands::AppSubCommand;
        
        let list_cmd = AppSubCommand::List { installed: false, running: false, detailed: false };
        assert_eq!(list_cmd.name(), "list");
        assert!(!list_cmd.requires_root());
        assert!(!list_cmd.is_mutating());
        
        let start_cmd = AppSubCommand::Start { 
            name: "test".to_string(), 
            debug: false, 
            args: None 
        };
        assert_eq!(start_cmd.name(), "start");
        assert!(start_cmd.requires_root());
        assert!(start_cmd.is_mutating());
        
        let install_cmd = AppSubCommand::Install { 
            dest: None, 
            packages: vec!["test".to_string()], 
            force: false 
        };
        assert_eq!(install_cmd.name(), "install");
        assert!(install_cmd.requires_root());
        assert!(install_cmd.is_mutating());
    }

    // Integration test helper
    async fn setup_test_environment() -> (TempDir, AppManager) {
        let temp_dir = create_test_dir();
        let scripts_dir = temp_dir.path().join("scripts");
        let links_dir = temp_dir.path().join("links");
        
        tokio::fs::create_dir_all(&scripts_dir).await.expect("Failed to create scripts dir");
        tokio::fs::create_dir_all(&links_dir).await.expect("Failed to create links dir");
        
        let config = AppManagerConfig::new()
            .with_script_path(scripts_dir)
            .with_link_path(links_dir)
            .with_dbus(false)
            .with_timeout(5); // Short timeout for tests

        let mut manager = AppManager::new(config).expect("Failed to create manager");
        manager.initialize().await.expect("Failed to initialize manager");
        
        (temp_dir, manager)
    }

    #[tokio::test]
    async fn test_integration_enable_disable_autostart() {
        let (_temp_dir, mut manager) = setup_test_environment().await;
        
        // Create a test script
        let script_content = r#"#!/bin/bash
start() { echo "start test"; }
stop() { echo "stop test"; }
"#;
        
        create_test_script(
            &manager.get_config().script_path, 
            "testapp", 
            script_content
        ).await.expect("Failed to create test script");
        
        // Rescan to pick up the new script
        manager.scan_applications().await.expect("Failed to scan applications");
        
        // Test enable auto-start
        manager.enable_auto_start("testapp").await.expect("Failed to enable auto-start");
        
        let app_info = manager.get_application_state("testapp").await.expect("Failed to get app state");
        assert_eq!(app_info.auto_start, AutoStartState::Enabled);
        
        // Test disable auto-start
        manager.disable_auto_start("testapp").await.expect("Failed to disable auto-start");
        
        let app_info = manager.get_application_state("testapp").await.expect("Failed to get app state");
        assert_eq!(app_info.auto_start, AutoStartState::Disabled);
    }
}
