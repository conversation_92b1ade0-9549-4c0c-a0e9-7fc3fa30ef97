
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use zvariant::{Type, Value};
use thiserror::Error;
use crate::package::PackageInfo;

/// Application management errors
#[derive(Error, Debug)]
pub enum AppError {
    #[error("Application '{0}' not found")]
    NotFound(String),

    #[error("Application '{0}' is already running")]
    AlreadyRunning(String),

    #[error("Application '{0}' is not running")]
    NotRunning(String),

    #[error("Failed to start application '{0}': {1}")]
    StartFailed(String, String),

    #[error("Failed to stop application '{0}': {1}")]
    StopFailed(String, String),

    #[error("Script error for application '{0}': {1}")]
    ScriptError(String, String),

    #[error("Configuration error: {0}")]
    ConfigError(String),

    #[error("Permission denied: {0}")]
    PermissionDenied(String),

    #[error("Timeout waiting for application '{0}' to {1}")]
    Timeout(String, String),

    #[error("Invalid state transition from {0} to {1} for application '{2}'")]
    InvalidStateTransition(AppState, AppState, String),

    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),

    #[error("Parse error: {0}")]
    ParseError(String),

    #[error("System error: {0}")]
    SystemError(String),

    #[error("D-Bus error: {0}")]
    DBusError(#[from] zbus::Error),

    #[error("Daemon error: {0}")]
    DaemonError(#[from] crate::daemon::DaemonError),

    #[error("Package management error: {0}")]
    PackageError(String),

    #[error("Serialization error: {0}")]
    SerializationError(#[from] serde_json::Error),

    #[error("Regex error: {0}")]
    RegexError(#[from] regex::Error),

    #[error("Join error: {0}")]
    JoinError(#[from] tokio::task::JoinError),
}

impl AppError {
    /// Create a new configuration error
    pub fn config<S: Into<String>>(msg: S) -> Self {
        Self::ConfigError(msg.into())
    }

    /// Create a new system error
    pub fn system<S: Into<String>>(msg: S) -> Self {
        Self::SystemError(msg.into())
    }

    /// Create a new parse error
    pub fn parse<S: Into<String>>(msg: S) -> Self {
        Self::ParseError(msg.into())
    }

    /// Create a new package error
    pub fn package<S: Into<String>>(msg: S) -> Self {
        Self::PackageError(msg.into())
    }

    /// Check if this error is recoverable
    pub fn is_recoverable(&self) -> bool {
        matches!(
            self,
            AppError::Timeout(_, _)
                | AppError::SystemError(_)
                | AppError::IoError(_)
                | AppError::DBusError(_)
        )
    }

    /// Check if this error requires user intervention
    pub fn requires_user_intervention(&self) -> bool {
        matches!(
            self,
            AppError::PermissionDenied(_)
                | AppError::ConfigError(_)
                | AppError::InvalidStateTransition(_, _, _)
        )
    }

    /// Get error category for logging
    pub fn category(&self) -> &'static str {
        match self {
            AppError::NotFound(_) => "not_found",
            AppError::AlreadyRunning(_) => "already_running",
            AppError::NotRunning(_) => "not_running",
            AppError::StartFailed(_, _) => "start_failed",
            AppError::StopFailed(_, _) => "stop_failed",
            AppError::ScriptError(_, _) => "script_error",
            AppError::ConfigError(_) => "config_error",
            AppError::PermissionDenied(_) => "permission_denied",
            AppError::Timeout(_, _) => "timeout",
            AppError::InvalidStateTransition(_, _, _) => "invalid_state",
            AppError::IoError(_) => "io_error",
            AppError::ParseError(_) => "parse_error",
            AppError::SystemError(_) => "system_error",
            AppError::DBusError(_) => "dbus_error",
            AppError::DaemonError(_) => "daemon_error",
            AppError::PackageError(_) => "package_error",
            AppError::SerializationError(_) => "serialization_error",
            AppError::RegexError(_) => "regex_error",
            AppError::JoinError(_) => "join_error",
        }
    }
}

/// Result type for application operations
pub type AppResult<T> = Result<T, AppError>;

/// Application running state
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, Type)]
pub enum AppState {
    /// Application is not running
    Stopped,
    /// Application is running
    Running,
    /// Application is starting
    Starting,
    /// Application is stopping
    Stopping,
    /// Application failed to start or crashed
    Failed,
    /// Application state is unknown
    Unknown,
}

impl Default for AppState {
    fn default() -> Self {
        AppState::Stopped
    }
}

impl std::fmt::Display for AppState {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AppState::Stopped => write!(f, "stopped"),
            AppState::Running => write!(f, "running"),
            AppState::Starting => write!(f, "starting"),
            AppState::Stopping => write!(f, "stopping"),
            AppState::Failed => write!(f, "failed"),
            AppState::Unknown => write!(f, "unknown"),
        }
    }
}

/// Application auto-start state
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, Type)]
pub enum AutoStartState {
    /// Auto-start is disabled
    Disabled,
    /// Auto-start is enabled
    Enabled,
}

impl Default for AutoStartState {
    fn default() -> Self {
        AutoStartState::Disabled
    }
}

impl std::fmt::Display for AutoStartState {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AutoStartState::Disabled => write!(f, "disabled"),
            AutoStartState::Enabled => write!(f, "enabled"),
        }
    }
}

/// Daemon monitoring state
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, Type)]
pub enum DaemonState {
    /// Daemon monitoring is disabled
    Disabled,
    /// Daemon monitoring is enabled
    Enabled,
    /// Daemon monitoring is suspended
    Suspended,
}

impl Default for DaemonState {
    fn default() -> Self {
        DaemonState::Disabled
    }
}

impl std::fmt::Display for DaemonState {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DaemonState::Disabled => write!(f, "disabled"),
            DaemonState::Enabled => write!(f, "enabled"),
            DaemonState::Suspended => write!(f, "suspended"),
        }
    }
}

/// Application configuration script information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppScript {
    /// Path to the application script
    pub path: PathBuf,
    /// Script stage number (from S<number> prefix)
    pub stage: Option<u32>,
    /// Executable name (from EXECUTE variable or package name)
    pub executable: Option<String>,
    /// Command line arguments (from ARGS variable)
    pub args: Option<String>,
    /// Version command (from VERSION_CMD variable)
    pub version_cmd: Option<String>,
    /// Whether script has custom start/stop functions
    pub has_custom_functions: bool,
}

impl Default for AppScript {
    fn default() -> Self {
        Self {
            path: PathBuf::new(),
            stage: None,
            executable: None,
            args: None,
            version_cmd: None,
            has_custom_functions: false,
        }
    }
}

/// Complete application information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppInfo {
    /// Package information
    pub package: Option<PackageInfo>,
    /// Application name
    pub name: String,
    /// Application version
    pub version: String,
    /// Current running state
    pub state: AppState,
    /// Auto-start configuration
    pub auto_start: AutoStartState,
    /// Daemon monitoring state
    pub daemon_state: DaemonState,
    /// Application script information
    pub script: Option<AppScript>,
    /// Last state change timestamp
    pub last_state_change: Option<std::time::SystemTime>,
}

impl Default for AppInfo {
    fn default() -> Self {
        Self {
            name: String::new(),
            version: String::new(),
            package: None,
            state: AppState::default(),
            auto_start: AutoStartState::default(),
            daemon_state: DaemonState::default(),
            script: None,
            last_state_change: None,
        }
    }
}

impl AppInfo {
    /// Create a new AppInfo with the given name
    pub fn new(name: String) -> Self {
        Self {
            name,
            ..Default::default()
        }
    }

    /// Create AppInfo from package name and version
    pub fn from_package(name: String, info: PackageInfo) -> Self {
        Self {
            name,
            version: info.version.clone(),
            package: Some(info),
            ..Default::default()
        }
    }

    /// Update the application state
    pub fn set_state(&mut self, new_state: AppState) {
        if self.state != new_state {
            self.state = new_state;
            self.last_state_change = Some(std::time::SystemTime::now());
        }
    }

    /// Update auto-start configuration
    pub fn set_auto_start(&mut self, auto_start: AutoStartState) {
        if self.auto_start != auto_start {
            self.auto_start = auto_start;
            self.last_state_change = Some(std::time::SystemTime::now());
        }
    }

    /// Update daemon state
    pub fn set_daemon_state(&mut self, daemon_state: DaemonState) {
        if self.daemon_state != daemon_state {
            self.daemon_state = daemon_state;
            self.last_state_change = Some(std::time::SystemTime::now());
        }
    }

    /// Update resource usage information
    // pub fn update_resource_usage(&mut self, usage: AppResourceUsage) {
    //     self.resource_usage = usage;
    // }

    /// Check if the application is currently running
    pub fn is_running(&self) -> bool {
        self.state == AppState::Running
    }

    /// Check if auto-start is enabled
    pub fn is_auto_start_enabled(&self) -> bool {
        self.auto_start == AutoStartState::Enabled
    }

    /// Check if daemon monitoring is active
    pub fn is_daemon_enabled(&self) -> bool {
        self.daemon_state == DaemonState::Enabled
    }

    /// Get the application's executable name
    pub fn get_executable(&self) -> Option<&str> {
        self.script.as_ref()
            .and_then(|s| s.executable.as_deref())
            .or_else(|| Some(&self.name))
    }

    /// Get the application's startup arguments
    pub fn get_args(&self) -> Option<&str> {
        self.script.as_ref()
            .and_then(|s| s.args.as_deref())
    }

    /// Get the version command for this application
    pub fn get_version_cmd(&self) -> String {
        self.script.as_ref()
            .and_then(|s| s.version_cmd.as_deref())
            .unwrap_or(&format!("{} -v", self.get_executable().unwrap_or(&self.name)))
            .to_string()
    }

    /// Get the startup stage number
    pub fn get_stage(&self) -> u32 {
        self.script.as_ref()
            .and_then(|s| s.stage)
            .unwrap_or(99) // Default stage if not specified
    }

    /// Convert to D-Bus compatible format (for State property)
    pub fn to_dbus_state(&self) -> (u8, u8, u8) {
        let running_state = match self.state {
            AppState::Stopped => 0,
            AppState::Running => 1,
            AppState::Starting => 2,
            AppState::Stopping => 3,
            AppState::Failed => 4,
            AppState::Unknown => 5,
        };

        let auto_start_state = match self.auto_start {
            AutoStartState::Disabled => 0,
            AutoStartState::Enabled => 1,
        };

        let daemon_state = match self.daemon_state {
            DaemonState::Disabled => 0,
            DaemonState::Enabled => 1,
            DaemonState::Suspended => 2,
        };

        (running_state, auto_start_state, daemon_state)
    }

    /// Create from D-Bus state format
    pub fn from_dbus_state(&mut self, state: (u8, u8, u8)) {
        let (running, auto_start, daemon) = state;

        self.state = match running {
            0 => AppState::Stopped,
            1 => AppState::Running,
            2 => AppState::Starting,
            3 => AppState::Stopping,
            4 => AppState::Failed,
            _ => AppState::Unknown,
        };

        self.auto_start = match auto_start {
            1 => AutoStartState::Enabled,
            _ => AutoStartState::Disabled,
        };

        self.daemon_state = match daemon {
            1 => DaemonState::Enabled,
            2 => DaemonState::Suspended,
            _ => DaemonState::Disabled,
        };

        self.last_state_change = Some(std::time::SystemTime::now());
    }
}