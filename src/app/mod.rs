mod app;
mod manager;
mod script;
mod commands;
mod dbus;

#[cfg(test)]
mod tests;

pub use app::{AppInfo, AppState, AutoStartState, DaemonState, AppScript, AppResourceUsage, AppError, AppResult};
pub use manager::AppManager;
pub use commands::{AppCommand, AppSubCommand};
pub use dbus::AppManagerDBus;

use std::path::PathBuf;

/// Default paths for application scripts and links
pub const FILE_PATH: &str = "/usr/lib/app-init.d";
pub const LINK_PATH: &str = "/etc/app-init.d";
pub const DEFAULT_INSTALL_ROOT: &str = "/usr/local";

/// Configuration for the application manager
#[derive(Debug, Clone)]
pub struct AppManagerConfig {
    /// Path to application scripts
    pub script_path: PathBuf,
    /// Path to auto-start links
    pub link_path: PathBuf,
    /// Default installation root
    pub install_root: PathBuf,
    /// Timeout for application start/stop operations (seconds)
    pub operation_timeout: u64,
    /// Interval for monitoring application status (seconds)
    pub monitor_interval: u64,
    /// Enable verbose logging
    pub verbose: bool,
}

impl Default for AppManagerConfig {
    fn default() -> Self {
        Self {
            script_path: PathBuf::from(FILE_PATH),
            link_path: PathBuf::from(LINK_PATH),
            install_root: PathBuf::from(DEFAULT_INSTALL_ROOT),
            operation_timeout: 30,
            monitor_interval: 10,
            verbose: false,
            enable_dbus: true,
        }
    }
}

impl AppManagerConfig {
    /// Create a new configuration with default values
    pub fn new() -> Self {
        Self::default()
    }

    /// Set the script path
    pub fn with_script_path<P: Into<PathBuf>>(mut self, path: P) -> Self {
        self.script_path = path.into();
        self
    }

    /// Set the link path
    pub fn with_link_path<P: Into<PathBuf>>(mut self, path: P) -> Self {
        self.link_path = path.into();
        self
    }

    /// Set the installation root
    pub fn with_install_root<P: Into<PathBuf>>(mut self, path: P) -> Self {
        self.install_root = path.into();
        self
    }

    /// Set the operation timeout
    pub fn with_timeout(mut self, timeout: u64) -> Self {
        self.operation_timeout = timeout;
        self
    }

    /// Set the monitor interval
    pub fn with_monitor_interval(mut self, interval: u64) -> Self {
        self.monitor_interval = interval;
        self
    }

    /// Enable verbose logging
    pub fn with_verbose(mut self, verbose: bool) -> Self {
        self.verbose = verbose;
        self
    }

    /// Enable or disable D-Bus interface
    pub fn with_dbus(mut self, enable: bool) -> Self {
        self.enable_dbus = enable;
        self
    }
}