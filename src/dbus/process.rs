use zbus::interface;
// use sysinfo::process;
use crate::monit::LoadAve;
use sysinfo::Process;

pub struct ProcessMonitor {
    inner: Process,
    load_ave: LoadAve,
}

#[interface(name = "mgc.platform.AppManager1.Proc")]
impl ProcessMonitor {
    #[zbus(property, name = "Pid")]
    fn pid(&self) -> u32 {
        self.inner.pid().as_u32()
    }

    #[zbus(property, name = "Uid")]
    fn uid(&self) -> u32 {
        if let Some(uid) = self.inner.user_id() {
            **uid
        } else {
            0
        }
    }

    #[zbus(property, name = "Status")]
    fn status(&self) -> String {
        self.inner.status().to_string()
    }

    #[zbus(property)]
    fn name(&self) -> String {
        self.inner.name().to_string_lossy().to_string()
    }

    #[zbus(property, name = "Uptime")]
    fn uptime(&self) -> u64 {
        self.inner.start_time()
    }

    #[zbus(property, name = "Runtime")]
    fn runtime(&self) -> u64 {
        self.inner.run_time()
    }

    #[zbus(property, name = "Cpuasge")]
    fn cpu_usage(&self) -> f32 {
        self.inner.cpu_usage()
    }

    #[zbus(property, name = "Load")]
    fn load(&self) -> (f32, f32, f32) {
        // TODO: Implement actual load average calculation
        (0.0, 0.0, 0.0)
    }
}
