4.4应用管理
4.4.1 需求分析：
1.智能运维需要获取应用基础信息和运行状态，包括版本信息，进程状态，CPU、内存占用，运行时间等。
2.对外提供统一的应用启停接口。支持额外参数使能日志记录和启停完成后的通知。
目前已通过app工具管理应用启停，但存在以下问题：
1.app工具仅提供命令行接口，上位机或云端使用需要依赖其他工具：如辅助工具。
2.目前工具仅以命令行返回码指示启停完成状态，无错误信息输出或仅输出到终端，这导致使用人员在判断应用启停完成状态时遇到困难，特别是对于开机自启项的执行结果。需要支持将启停结果输出到日志，并提供主动通知的机制。
3.统一管理应用程序及依赖项的安装、移除：现有应用的安装需要开发人员自行组织部署包和编写部署脚本，较为繁琐且难以管理。
4.4.2 概要设计：
4.4.2.1 外部依赖与假设：
1.从外部软件包仓库获取软件包，包仓库可以部署在上位机或云端
2.使用opkg作为包管理工具
3.软件包的依赖自动安装、更新依赖包仓库维护和更新索引
4.假设包仓库可以上传软件包并更新索引
4.4.2.2 模块分解描述：
1.启停、运行状态
目的：提供统一的启停接口和应用程序的运行状态、自启状态、守护进程状态、 版本等信息
功能列表：
1）启停应用
2）设置应用的自启状态
3）开启/关闭对应应用的守护进程
4）获取应用的版本
5）上述状态发生变化时的通知
功能描述：
1）应用启停对外提供统一的应用启停管理视图，所有受控程序（通过应用管理安装的程序）均以app start /stop <包名>的方式启停。以自动测试工具为例，假设安装时的包名（control中Package字段定义）为aut，则可以使用app start aut和app stop aut启停应用。
2）应用自启
开启应用自启后，系统启动时将依序调用app start启动应用。
3）开启/关闭守护进程
通过app start启动的程序将自动开启守护进程，通过app stop停止的程序将自动关闭守护进程。支持通过参数以debug模式启动程序，此时不自动开启守护进程，方便调试。
自启程序由于使用app start启动，将自动开启守护进程。
守护进程：
a)默认行为是当监视的进程意外退出或者僵死，守护进程负责重新拉起并通知
b)可以设置被监视进程资源阈值，当达到阈值后触发对应动作并通知。目前资源监视仅支持监视cpu和内存占用
4）获取应用版本
control的Version字段应包含软件包的版本信息，若应用本身支持参数获取版本信息，则以后者为准。一般二者应保持一致。
在应用配置脚本使用VERSION_CMD变量设置完整的获取应用版本的命令，如mgcver -p，此变量将覆盖默认的 <可执行程序名> -v 命令
5）当受控应用的运行状态、自启状态、版本、守护进程状态发送改变时，将发出通知，其他程序可以订阅该信号。
2.包管理
目的：
统一、规范的应用程序及依赖项的安装、移除
功能列表：
1）安装、移除应用
2）更新、列出所有可用软件包列表
3）升级应用
4）列出所有已安装软件和版本
5）列出所有已安装且可升级的软件和版本
6）配置软件包仓库地址
7）设置安装根目录
功能描述：
使用opkg作为包管理工具。opkg的包以后缀.ipk结尾，包结构与debian软件包deb基本相同，一个典型的ipk包结构包含两个主要部分：
a)data.tar.gz: 
实际部署的文件，在data目录下以相对路径形式组织，并最终以该相对路径安装到目标根目录下。如data/etc/config/app.conf，若指定目标根目录为 / ，则最终安装路径为/etc/config/app.conf，若指定目标根目录为/usr/local，则最终安装路径为/usr/local/etc/config/app.conf。
b)control.tar.gz: 
包含preinst、postinst、prerm、postrm脚本，分别用于执行安装前、安装后、移除前、移除后的额外操作，一般无需编写。
还包含一个control文件，在其中定义了软件包的基本信息，如Package字段定义了软件包的包名，Version字段定义了软件的版本，Depends字段定义该软件包依赖的其他软件包。
1）安装、移除应用：
安装应用将安装软件包，data目录下的文件将以相对路径形式安装到目标根目录下，并执行CONTROL内的preinst、postinst，如果软件包的control文件内Depends定义了依赖关系，并且包仓库中存在索引，依赖的软件包将自动被安装。
移除应用将从安装路径移除软件包data中的所有文件，可以通过参数指定由Depends自动安装的是否被移除。
2）更新、列出所有可用软件包列表：
从配置的包仓库更新软件包列表
3）升级应用：
若包仓库中软件包的Version字段大于已安装的，可进行应用升级，应用升级将移除旧版本应用并安装新版本应用。不支持版本回滚。
升级完成后应用将继承之前的运行状态以及自启和守护进程开启状态。
4）设置安装根目录：
默认安装根目录为/usr/local，若有其他安装路径需求，可以设置其他安装根目录。
4.4.2.2 对外接口
1. 命令行接口
app
子命令
子命令	参数	说明
list		列出已下载应用及其自启状态、运行状态
start	-d	启动程序，开启守护进程。
-d debug模式，此方式启动程序不开启守护进程
stop		停止程序，关闭守护进程。
enable		
disable		
ver		
state		
daemon	<cmd>
-s   <on | off>	启停对应进程的守护进程
install	[-d dest] <pkgs>	dest：安装根路径，未指定则默认/usr/local
pkgs: 要安装的一个或多个软件包
remove	<pkgs>	
update		更新可安装软件包列表并输出
upgrade		
list-upgradable		

2. 软件接口
D-Bus对象：/mgc/platform/AppManager1
接口：mgc.platform.AppManger1
方法	参数	说明
Run	IN    a(vb)	设置一组应用启停(v: id, b: state)
SetAutoStart	IN    a(vb)	设置一组应用自启状态(v: id, b: state)
Daemonize	IN    a(vb)	设置一组应用的守护进程状态(v: id, b: state)
State	IN    av  id
OUT  as  states	输入一组进程的id(pid或进程名)，输出一组状态，类似：转为json格式
Ver	IN    av  id
OUT  as  ver	
Install	IN    sas	安装根路径、安装软件的名称
Remove	IN    as	移除软件的名称
Update	OUT  as	更新可用软件包列表
Upgrade	IN    as	升级软件
ListInstalled	OUT  as	返回已安装的软件和版本
ListUpgradable	OUT  as	返回已安装的可升级的软件
AddPackageSource	IN    as	添加软件包仓库的url
DelPackageSource	IN    as	


受管控的应用将存在D-Bus对象路径：/mgc/platform/AppManager1/{name},
接口：mgc.platform.AppManager1.Apps
方法	参数	说明
Run	IN    b	输入布尔值，启停应用
AutoStart	IN    b	
Daemonize	IN    b	
NotifyPropsChange	IN    b	设置是否启用属性改变通知
Limit	IN    a{sv}	
可以使用标准接口org.freedesktop.DBus.ObjectManager.GetManagedObjects获取所有对象、接口和属性（类型：a{oa{sa{sv}}}）


属性	访问权限	类型	说明
State	可读	(yyy) 	运行状态、自启状态、守护进程状态
Ver	可读	s	版本
可以使用标准接口org.freedesktop.DBus.Properties.PropertiesChanged发送信号

接口：mgc.platform.AppManager1.Cgroup
方法	参数	说明
Limit	IN   b  on/off	使用/不使用限制

属性	访问权限	类型	说明
Cpu	可读可写	u	限制进程cpu使用上限
Mem	可读可写	u	限制进程内存使用上限
Blkio	可读可写	u	限制进程块设备io速度（需要更新系统，不兼容旧版）

接口：mgc.platform.AppManager1.Daemon
属性	访问权限	类型	说明
Run	可读可写	b	0：停止守护进程
1：开启守护进程
Suspend	可读可写	b	0: 不挂起检测任务
1：挂起检测任务
LoadThr	可读可写	(us)	u: 阈值
s: 动作（可选warn、restart、...）
RamThr	可读可写	(us)	u: 阈值
s: 动作（可选warn、restart、...）
LoadLimit	可读可写	v	
RamLimit	可读可写	v	
DiskIOLimit	可读可写	v	

信号	参数	说明
WarnLoad	u	超过阈值的告警信号，与超限时的值
WarnRam	u	

4.4.2.3 运行设计
1. 规范与约束：
a) 若安装的软件包中未包含应用配置脚本，启动时将尝试不带参数直接调用与包名相同的可执行程序，停止时将尝试kill程序（发送SIGTERM）。
b) 若程序存在以上行为不能满足的启停需求，请在安装软件包中包含应用配置脚本。
c) 应用配置脚本规范：
若需要启动时传入参数，使用ARGS变量设置启动时传入的参数；
若可执行程序名与包名不同，使用EXECUTE变量设置实际可执行程序名称；
若仍需要额外的启停操作，在应用配置脚本中以名为start和stop的函数形式提供，该方式将覆盖上述启停行为，此时ARGS和EXECUTE设置的参数不生效。
应用配置脚本名称支持两种形式：S<两位数字> <包名> 或仅<包名>，例如S80aut或aut，名称中的数字仅影响自启顺序，包名部分与control中Package字段相同。当应用配置脚本名称仅为包名时，自启时将根据control中的Depends字段按依赖顺序启动程序。
c)软件包组织形式规范：
软件包CONTROL目录下的control文件中，应至少包含以下字段：
Package：软件包名称
Version：软件版本
	Architecture：应用适用的架构，脚本可以为all，二进制程序根据架构选择，如arm_cortex-a7或aarch64
可选：
Depends：依赖的其他软件包名称，可以在名称后指定版本约束，如libc (>>2.38)，多个依赖用逗号分隔。括号内不要包含空格。可以使用>> 大于，>= 大于等于， = 等于， <= 小于等于，<< 小于。
Description：关于软件的简短描述
Conflicts：与该包冲突的其他包，将要安装的包与已安装的包存在冲突时将无法安装。
Replaces：被该包替代的包，语法与Depends相同。当与Conflicts一起用时，安装时将自动移除被替代的包。
Provides：声明该包提供的包。以mgc为例，假如mgc包内包含comm包，若不在Provides中声明提供comm，则安装后其他依赖comm的包将找不到依赖。
整理Confilcts、Provides、Replaces单独使用和组合使用时的行为，未列出的组合行为没有特殊的效果。
Conflicts	互相冲突的包无法同时安装
Provides	用于单个包提供多个依赖
Replaces	单独使用无特殊效果，更类似于一个标志位与其他字段组合使用。
Conflicts + Replaces	该组合将更改冲突时的行为。假设包B中的Conflicts和Replaces字段中都存在包A，在已安装包A的设备上安装包B时将自动移除包A，已安装包B的设备上无法安装包A。
即在冲突的基础上提供替代行为，若冲突二者只能存在一个则一定是替代方存在。
Conflicts + Replaces + Provides	严格的替代。在上个例子中，若包B的Provides字段中存在包A，则尝试安装包A时将直接安装包B。已安装的包A将在安装包B时被移除。

Conffiles：指定配置文件和md5校验，配置文件以绝对路径形式给出，安装和移除时将不会修改绝对路径下已存在的配置文件。
配置格式为每个配置文件一行并开头空一格。如：




除非在postinst脚本中做额外处理，否则安装后文件权限将与打包时data/内的文件权限相同，请在打包时给予文件应有的权限。
软件包的默认安装根路径为/usr/local，在此路径下安装应用时，软件包data目录下的文件应按如下规则组织：
①bin、sbin目录下放可执行程序，一般bin目录下为所有用户均可执行的程序，sbin目录下为超级用户可执行程序。目前没做区分；
②lib目录下放程序的依赖库；
③etc目录下存放程序配置。这里的配置应为默认配置或配置模板，并在名称上与实际使用的配置文件做区分，以避免在移除或升级软件包时移除实际的运行配置。

一个软件包示例结构如下：
example/
├── CONTROL
│   ├── control
│   ├── postinst
│   ├── postrm
│   ├── preinst
│   └── prerm
└── data
    ├── bin
    │   └── app
    ├── etc
    │   └── app.conf
    └── lib
        └── libapp.so
安装到/usr/local目录以外的软件包不受上述data组织规范约束，请自行合理安排安装路径。
2.交互说明
应用管理可以通过上位机、云端页面和命令行接口使用，以使用命令行接口为例，说明应用管理的各功能之间如何配合：
在按照规范组织好一个软件包后，使用打包工具（后续提供工具及其使用说明）生成ipk包。应用的部署、使用、移除示例如下图
