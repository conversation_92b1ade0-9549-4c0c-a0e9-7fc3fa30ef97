[package]
name = "app"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
anyhow = "1.0.86"
clap = {version = "4.5.4", features = ["derive"]}
env_logger = "0.11.8"
lazy_static = "1.5.0"
log = "0.4"
procfs = "0.17.0"
# psutil = {version = "5.3.0", default-features = false, features = ["process"]}
regex = "1.11.1"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0"
sysinfo = "0.35.2"
thiserror = "2.0.12"
tokio = { version = "1.46.1", features = ["full"] }
watchmend = "0.0.1"
zbus = "5.8.0"
zvariant = "5.6.0"

[dev-dependencies]
tempfile = "3.0"

[features]
default = ["opkg"]
opkg = []
