use app::app::{<PERSON><PERSON><PERSON><PERSON><PERSON>, AppManagerConfig, AppState, AutoStartState};
use std::path::PathBuf;
use tempfile::TempDir;
use tokio;

/// Integration tests for the app module
#[tokio::test]
async fn test_app_manager_full_workflow() {
    let temp_dir = create_test_environment().await;
    let mut manager = create_test_manager(&temp_dir).await;

    // Test 1: Initial scan should find no applications
    let apps = manager.list_applications(true, false).await.unwrap();
    assert_eq!(apps.len(), 0, "Should start with no applications");

    // Test 2: Create test scripts and rescan
    create_test_scripts(&temp_dir).await;
    manager.scan_applications().await.unwrap();
    
    let apps = manager.list_applications(true, false).await.unwrap();
    assert_eq!(apps.len(), 2, "Should find 2 test applications");

    // Test 3: Check application names and stages
    let app1 = apps.iter().find(|app| app.name == "testapp1").unwrap();
    let app2 = apps.iter().find(|app| app.name == "testapp2").unwrap();
    
    assert_eq!(app1.get_stage(), 10);
    assert_eq!(app2.get_stage(), 20);
    assert_eq!(app1.auto_start, AutoStartState::Disabled);
    assert_eq!(app2.auto_start, AutoStartState::Disabled);

    // Test 4: Enable auto-start for app1
    manager.enable_auto_start("testapp1").await.unwrap();
    let app1_state = manager.get_application_state("testapp1").await.unwrap();
    assert_eq!(app1_state.auto_start, AutoStartState::Enabled);

    // Test 5: Start application
    manager.start_application("testapp1", false).await.unwrap();
    let app1_state = manager.get_application_state("testapp1").await.unwrap();
    assert_eq!(app1_state.state, AppState::Running);

    // Test 6: Stop application
    manager.stop_application("testapp1", false).await.unwrap();
    let app1_state = manager.get_application_state("testapp1").await.unwrap();
    assert_eq!(app1_state.state, AppState::Stopped);

    // Test 7: Disable auto-start
    manager.disable_auto_start("testapp1").await.unwrap();
    let app1_state = manager.get_application_state("testapp1").await.unwrap();
    assert_eq!(app1_state.auto_start, AutoStartState::Disabled);
}

#[tokio::test]
async fn test_auto_start_all_applications() {
    let temp_dir = create_test_environment().await;
    let mut manager = create_test_manager(&temp_dir).await;
    
    create_test_scripts(&temp_dir).await;
    manager.scan_applications().await.unwrap();

    // Enable auto-start for all applications
    manager.enable_auto_start("testapp1").await.unwrap();
    manager.enable_auto_start("testapp2").await.unwrap();

    // Start all auto-start applications
    manager.start_auto_start_applications().await.unwrap();

    // Check that both applications are running
    let apps = manager.list_applications(true, true).await.unwrap();
    assert_eq!(apps.len(), 2, "Both applications should be running");

    // Stop all applications
    manager.stop_all_applications(false).await.unwrap();

    // Check that all applications are stopped
    let running_apps = manager.list_applications(true, true).await.unwrap();
    assert_eq!(running_apps.len(), 0, "No applications should be running");
}

#[tokio::test]
async fn test_application_version_retrieval() {
    let temp_dir = create_test_environment().await;
    let manager = create_test_manager(&temp_dir).await;
    
    create_test_scripts(&temp_dir).await;
    
    // This should fail since we haven't scanned yet
    let result = manager.get_application_version("testapp1").await;
    assert!(result.is_err(), "Should fail to get version for non-existent app");
}

#[tokio::test]
async fn test_error_handling() {
    let temp_dir = create_test_environment().await;
    let mut manager = create_test_manager(&temp_dir).await;

    // Test starting non-existent application
    let result = manager.start_application("nonexistent", false).await;
    assert!(result.is_err(), "Should fail to start non-existent application");

    // Test stopping non-existent application
    let result = manager.stop_application("nonexistent", false).await;
    assert!(result.is_err(), "Should fail to stop non-existent application");

    // Test enabling auto-start for non-existent application
    let result = manager.enable_auto_start("nonexistent").await;
    assert!(result.is_err(), "Should fail to enable auto-start for non-existent application");
}

#[tokio::test]
async fn test_package_management_placeholders() {
    let temp_dir = create_test_environment().await;
    let mut manager = create_test_manager(&temp_dir).await;

    // Test package installation (should fail with placeholder message)
    let result = manager.install_packages(vec!["testpackage".to_string()], None).await;
    assert!(result.is_err(), "Package installation should fail (not implemented)");

    // Test package removal (should fail with placeholder message)
    let result = manager.remove_packages(vec!["testpackage".to_string()]).await;
    assert!(result.is_err(), "Package removal should fail (not implemented)");

    // Test package index update (should fail with placeholder message)
    let result = manager.update_package_index().await;
    assert!(result.is_err(), "Package index update should fail (not implemented)");

    // Test package upgrade (should fail with placeholder message)
    let result = manager.upgrade_packages(vec!["testpackage".to_string()]).await;
    assert!(result.is_err(), "Package upgrade should fail (not implemented)");
}

#[tokio::test]
async fn test_configuration_options() {
    let temp_dir = create_test_environment().await;
    
    // Test custom configuration
    let config = AppManagerConfig::new()
        .with_script_path(temp_dir.path().join("custom_scripts"))
        .with_link_path(temp_dir.path().join("custom_links"))
        .with_timeout(60)
        .with_monitor_interval(30)
        .with_verbose(true)
        .with_dbus(false);

    assert_eq!(config.operation_timeout, 60);
    assert_eq!(config.monitor_interval, 30);
    assert!(config.verbose);
    assert!(!config.enable_dbus);

    let manager = AppManager::new(config).unwrap();
    assert_eq!(manager.get_config().operation_timeout, 60);
}

// Helper functions

async fn create_test_environment() -> TempDir {
    let temp_dir = tempfile::tempdir().expect("Failed to create temp dir");
    
    let scripts_dir = temp_dir.path().join("scripts");
    let links_dir = temp_dir.path().join("links");
    
    tokio::fs::create_dir_all(&scripts_dir).await.expect("Failed to create scripts dir");
    tokio::fs::create_dir_all(&links_dir).await.expect("Failed to create links dir");
    
    temp_dir
}

async fn create_test_manager(temp_dir: &TempDir) -> AppManager {
    let config = AppManagerConfig::new()
        .with_script_path(temp_dir.path().join("scripts"))
        .with_link_path(temp_dir.path().join("links"))
        .with_dbus(false)
        .with_timeout(5);

    let mut manager = AppManager::new(config).expect("Failed to create manager");
    manager.initialize().await.expect("Failed to initialize manager");
    
    manager
}

async fn create_test_scripts(temp_dir: &TempDir) {
    let scripts_dir = temp_dir.path().join("scripts");
    
    let script1_content = r#"#!/bin/bash
EXECUTE=sleep
ARGS="10"
VERSION_CMD="echo 'TestApp1 v1.0.0'"

start() {
    echo "Starting testapp1"
    sleep 10 &
    echo $! > /tmp/testapp1.pid
}

stop() {
    echo "Stopping testapp1"
    if [ -f /tmp/testapp1.pid ]; then
        kill $(cat /tmp/testapp1.pid) 2>/dev/null
        rm -f /tmp/testapp1.pid
    fi
}

version() {
    echo "TestApp1 v1.0.0"
}
"#;

    let script2_content = r#"#!/bin/bash
EXECUTE=sleep
ARGS="20"
VERSION_CMD="echo 'TestApp2 v2.0.0'"

start() {
    echo "Starting testapp2"
    sleep 20 &
    echo $! > /tmp/testapp2.pid
}

stop() {
    echo "Stopping testapp2"
    if [ -f /tmp/testapp2.pid ]; then
        kill $(cat /tmp/testapp2.pid) 2>/dev/null
        rm -f /tmp/testapp2.pid
    fi
}

version() {
    echo "TestApp2 v2.0.0"
}
"#;

    write_test_script(&scripts_dir, "S10testapp1", script1_content).await;
    write_test_script(&scripts_dir, "S20testapp2", script2_content).await;
}

async fn write_test_script(dir: &PathBuf, name: &str, content: &str) {
    let script_path = dir.join(name);
    tokio::fs::write(&script_path, content).await.expect("Failed to write script");
    
    // Make executable
    use std::os::unix::fs::PermissionsExt;
    let mut perms = tokio::fs::metadata(&script_path).await.expect("Failed to get metadata").permissions();
    perms.set_mode(0o755);
    tokio::fs::set_permissions(&script_path, perms).await.expect("Failed to set permissions");
}
