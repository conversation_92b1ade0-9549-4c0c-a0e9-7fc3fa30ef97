#!/bin/bash

# Build script for the application manager

set -e

echo "=== Application Manager Build Script ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Rust is installed
check_rust() {
    print_status "Checking Rust installation..."
    if ! command -v rustc &> /dev/null; then
        print_error "Rust is not installed. Please install Rust from https://rustup.rs/"
        exit 1
    fi
    
    local rust_version=$(rustc --version)
    print_success "Rust found: $rust_version"
}

# Check dependencies
check_dependencies() {
    print_status "Checking system dependencies..."
    
    # Check for required system packages
    local missing_deps=()
    
    if ! command -v pkg-config &> /dev/null; then
        missing_deps+=("pkg-config")
    fi
    
    if ! command -v gcc &> /dev/null; then
        missing_deps+=("gcc")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_warning "Missing system dependencies: ${missing_deps[*]}"
        print_status "On Ubuntu/Debian, install with: sudo apt-get install ${missing_deps[*]}"
        print_status "On CentOS/RHEL, install with: sudo yum install ${missing_deps[*]}"
    else
        print_success "All system dependencies found"
    fi
}

# Format code
format_code() {
    print_status "Formatting code..."
    if cargo fmt --check &> /dev/null; then
        print_success "Code is already formatted"
    else
        print_status "Formatting code..."
        cargo fmt
        print_success "Code formatted"
    fi
}

# Run clippy for linting
run_clippy() {
    print_status "Running clippy for linting..."
    if cargo clippy --all-targets --all-features -- -D warnings; then
        print_success "Clippy checks passed"
    else
        print_warning "Clippy found some issues"
    fi
}

# Build the project
build_project() {
    print_status "Building project..."
    
    # Build in debug mode
    print_status "Building debug version..."
    if cargo build; then
        print_success "Debug build completed"
    else
        print_error "Debug build failed"
        exit 1
    fi
    
    # Build in release mode
    print_status "Building release version..."
    if cargo build --release; then
        print_success "Release build completed"
    else
        print_error "Release build failed"
        exit 1
    fi
}

# Run tests
run_tests() {
    print_status "Running tests..."
    
    # Run unit tests
    print_status "Running unit tests..."
    if cargo test --lib; then
        print_success "Unit tests passed"
    else
        print_error "Unit tests failed"
        exit 1
    fi
    
    # Run integration tests
    print_status "Running integration tests..."
    if cargo test --test integration_test; then
        print_success "Integration tests passed"
    else
        print_warning "Integration tests failed or not found"
    fi
    
    # Run doc tests
    print_status "Running doc tests..."
    if cargo test --doc; then
        print_success "Doc tests passed"
    else
        print_warning "Doc tests failed"
    fi
}

# Generate documentation
generate_docs() {
    print_status "Generating documentation..."
    if cargo doc --no-deps; then
        print_success "Documentation generated"
        print_status "Open target/doc/app/index.html to view documentation"
    else
        print_warning "Documentation generation failed"
    fi
}

# Run example
run_example() {
    print_status "Running demo example..."
    if cargo run --example app_manager_demo; then
        print_success "Demo example completed"
    else
        print_warning "Demo example failed"
    fi
}

# Check binary size
check_binary_size() {
    print_status "Checking binary sizes..."
    
    if [ -f "target/debug/app" ]; then
        local debug_size=$(du -h target/debug/app | cut -f1)
        print_status "Debug binary size: $debug_size"
    fi
    
    if [ -f "target/release/app" ]; then
        local release_size=$(du -h target/release/app | cut -f1)
        print_status "Release binary size: $release_size"
    fi
}

# Install binary
install_binary() {
    print_status "Installing binary..."
    
    local install_dir="${1:-/usr/local/bin}"
    
    if [ ! -f "target/release/app" ]; then
        print_error "Release binary not found. Run build first."
        exit 1
    fi
    
    if [ ! -w "$install_dir" ]; then
        print_status "Installing to $install_dir (requires sudo)..."
        sudo cp target/release/app "$install_dir/"
        sudo chmod +x "$install_dir/app"
    else
        print_status "Installing to $install_dir..."
        cp target/release/app "$install_dir/"
        chmod +x "$install_dir/app"
    fi
    
    print_success "Binary installed to $install_dir/app"
}

# Clean build artifacts
clean_build() {
    print_status "Cleaning build artifacts..."
    cargo clean
    print_success "Build artifacts cleaned"
}

# Main function
main() {
    local command="${1:-all}"
    
    case "$command" in
        "check")
            check_rust
            check_dependencies
            ;;
        "format")
            format_code
            ;;
        "lint")
            run_clippy
            ;;
        "build")
            check_rust
            build_project
            check_binary_size
            ;;
        "test")
            run_tests
            ;;
        "docs")
            generate_docs
            ;;
        "example")
            run_example
            ;;
        "install")
            install_binary "$2"
            ;;
        "clean")
            clean_build
            ;;
        "all")
            check_rust
            check_dependencies
            format_code
            run_clippy
            build_project
            run_tests
            generate_docs
            check_binary_size
            print_success "All tasks completed successfully!"
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  check     - Check Rust and system dependencies"
            echo "  format    - Format code using rustfmt"
            echo "  lint      - Run clippy for linting"
            echo "  build     - Build debug and release versions"
            echo "  test      - Run all tests"
            echo "  docs      - Generate documentation"
            echo "  example   - Run demo example"
            echo "  install   - Install binary (default: /usr/local/bin)"
            echo "  clean     - Clean build artifacts"
            echo "  all       - Run all tasks (default)"
            echo "  help      - Show this help message"
            ;;
        *)
            print_error "Unknown command: $command"
            echo "Run '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
