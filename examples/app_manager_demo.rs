use app::app::{App<PERSON><PERSON>ger, AppManagerConfig, AppCommand, AppSubCommand};
use std::path::PathBuf;
use tokio;
use log::{info, error};

/// Demo application showing how to use the AppManager
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Info)
        .init();

    info!("Starting AppManager Demo");

    // Create a temporary directory for demo
    let temp_dir = tempfile::tempdir()?;
    let scripts_dir = temp_dir.path().join("scripts");
    let links_dir = temp_dir.path().join("links");
    
    // Create directories
    tokio::fs::create_dir_all(&scripts_dir).await?;
    tokio::fs::create_dir_all(&links_dir).await?;

    // Create demo configuration
    let config = AppManagerConfig::new()
        .with_script_path(scripts_dir.clone())
        .with_link_path(links_dir.clone())
        .with_timeout(10)
        .with_verbose(true)
        .with_dbus(false); // Disable D-Bus for demo

    // Create and initialize manager
    let mut manager = AppManager::new(config)?;
    manager.initialize().await?;

    info!("AppManager initialized successfully");

    // Create some demo application scripts
    create_demo_scripts(&scripts_dir).await?;

    // Demonstrate various operations
    demo_basic_operations(&mut manager).await?;
    demo_auto_start_management(&mut manager).await?;
    demo_application_lifecycle(&mut manager).await?;

    info!("Demo completed successfully");
    Ok(())
}

/// Create demo application scripts
async fn create_demo_scripts(scripts_dir: &PathBuf) -> Result<(), Box<dyn std::error::Error>> {
    info!("Creating demo application scripts");

    // Create a simple web server script
    let webserver_script = r#"#!/bin/bash
# Demo web server application
EXECUTE=python3
ARGS="-m http.server 8080"
VERSION_CMD="python3 --version"

start() {
    echo "Starting demo web server on port 8080"
    python3 -m http.server 8080 > /dev/null 2>&1 &
    echo $! > /tmp/webserver.pid
}

stop() {
    echo "Stopping demo web server"
    if [ -f /tmp/webserver.pid ]; then
        kill $(cat /tmp/webserver.pid) 2>/dev/null
        rm -f /tmp/webserver.pid
    fi
}

version() {
    python3 --version
}
"#;

    // Create a database script
    let database_script = r#"#!/bin/bash
# Demo database application
EXECUTE=sleep
ARGS="3600"
VERSION_CMD="echo 'Database v1.0.0'"

start() {
    echo "Starting demo database"
    sleep 3600 &
    echo $! > /tmp/database.pid
}

stop() {
    echo "Stopping demo database"
    if [ -f /tmp/database.pid ]; then
        kill $(cat /tmp/database.pid) 2>/dev/null
        rm -f /tmp/database.pid
    fi
}

version() {
    echo "Database v1.0.0"
}
"#;

    // Create a monitoring script
    let monitor_script = r#"#!/bin/bash
# Demo monitoring application
EXECUTE=watch
ARGS="-n 5 date"
VERSION_CMD="echo 'Monitor v2.1.0'"

start() {
    echo "Starting demo monitor"
    watch -n 5 date > /dev/null 2>&1 &
    echo $! > /tmp/monitor.pid
}

stop() {
    echo "Stopping demo monitor"
    if [ -f /tmp/monitor.pid ]; then
        kill $(cat /tmp/monitor.pid) 2>/dev/null
        rm -f /tmp/monitor.pid
    fi
}

version() {
    echo "Monitor v2.1.0"
}
"#;

    // Write scripts with proper permissions
    write_script(scripts_dir, "S10database", database_script).await?;
    write_script(scripts_dir, "S20webserver", webserver_script).await?;
    write_script(scripts_dir, "S30monitor", monitor_script).await?;

    info!("Created 3 demo application scripts");
    Ok(())
}

/// Write a script file with executable permissions
async fn write_script(dir: &PathBuf, name: &str, content: &str) -> Result<(), Box<dyn std::error::Error>> {
    let script_path = dir.join(name);
    tokio::fs::write(&script_path, content).await?;
    
    // Make executable
    use std::os::unix::fs::PermissionsExt;
    let mut perms = tokio::fs::metadata(&script_path).await?.permissions();
    perms.set_mode(0o755);
    tokio::fs::set_permissions(&script_path, perms).await?;
    
    Ok(())
}

/// Demonstrate basic operations
async fn demo_basic_operations(manager: &mut AppManager) -> Result<(), Box<dyn std::error::Error>> {
    info!("=== Demo: Basic Operations ===");

    // Scan for applications
    manager.scan_applications().await?;
    info!("Scanned applications");

    // List all applications
    let apps = manager.list_applications(true, false).await?;
    info!("Found {} applications:", apps.len());
    for app in &apps {
        info!("  - {} (stage: {}, auto-start: {})", 
              app.name, app.get_stage(), app.auto_start);
    }

    // Get version information
    for app in &apps {
        match manager.get_application_version(&app.name).await {
            Ok(version) => info!("  {} version: {}", app.name, version),
            Err(e) => error!("  Failed to get version for {}: {}", app.name, e),
        }
    }

    Ok(())
}

/// Demonstrate auto-start management
async fn demo_auto_start_management(manager: &mut AppManager) -> Result<(), Box<dyn std::error::Error>> {
    info!("=== Demo: Auto-start Management ===");

    // Enable auto-start for database and webserver
    manager.enable_auto_start("database").await?;
    info!("Enabled auto-start for database");

    manager.enable_auto_start("webserver").await?;
    info!("Enabled auto-start for webserver");

    // List applications with auto-start status
    let apps = manager.list_applications(true, false).await?;
    info!("Auto-start status:");
    for app in &apps {
        info!("  {} auto-start: {}", app.name, app.auto_start);
    }

    // Disable auto-start for monitor
    manager.disable_auto_start("monitor").await?;
    info!("Disabled auto-start for monitor");

    Ok(())
}

/// Demonstrate application lifecycle
async fn demo_application_lifecycle(manager: &mut AppManager) -> Result<(), Box<dyn std::error::Error>> {
    info!("=== Demo: Application Lifecycle ===");

    // Start applications in dependency order
    info!("Starting applications in dependency order...");
    
    // Start database first (S10)
    match manager.start_application("database", false).await {
        Ok(()) => info!("✓ Started database"),
        Err(e) => error!("✗ Failed to start database: {}", e),
    }

    // Wait a bit
    tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;

    // Start webserver (S20)
    match manager.start_application("webserver", false).await {
        Ok(()) => info!("✓ Started webserver"),
        Err(e) => error!("✗ Failed to start webserver: {}", e),
    }

    // Wait a bit
    tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;

    // Start monitor (S30)
    match manager.start_application("monitor", false).await {
        Ok(()) => info!("✓ Started monitor"),
        Err(e) => error!("✗ Failed to start monitor: {}", e),
    }

    // Check running status
    info!("Checking application status...");
    let running_apps = manager.list_applications(true, true).await?;
    info!("Running applications: {}", running_apps.len());
    for app in &running_apps {
        info!("  ✓ {} is running (PID: {:?})", 
              app.name, app.resource_usage.pid);
    }

    // Wait a bit to let applications run
    tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

    // Stop applications in reverse order
    info!("Stopping applications in reverse order...");

    match manager.stop_application("monitor", false).await {
        Ok(()) => info!("✓ Stopped monitor"),
        Err(e) => error!("✗ Failed to stop monitor: {}", e),
    }

    match manager.stop_application("webserver", false).await {
        Ok(()) => info!("✓ Stopped webserver"),
        Err(e) => error!("✗ Failed to stop webserver: {}", e),
    }

    match manager.stop_application("database", false).await {
        Ok(()) => info!("✓ Stopped database"),
        Err(e) => error!("✗ Failed to stop database: {}", e),
    }

    // Final status check
    let final_apps = manager.list_applications(true, false).await?;
    info!("Final application status:");
    for app in &final_apps {
        info!("  {} state: {}", app.name, app.state);
    }

    Ok(())
}

/// Demonstrate command-line interface usage
#[allow(dead_code)]
fn demo_command_line_usage() {
    info!("=== Demo: Command Line Usage ===");
    
    // Examples of how to use the command line interface
    let examples = vec![
        "app list --installed",
        "app start database",
        "app stop webserver --force",
        "app enable database",
        "app disable monitor",
        "app ver database",
        "app state webserver --format json",
        "app daemon database start",
        "app install --dest /opt mypackage",
        "app remove oldpackage",
        "app update",
        "app upgrade",
    ];

    info!("Command line examples:");
    for example in examples {
        info!("  $ {}", example);
    }
}
